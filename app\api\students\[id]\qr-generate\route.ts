import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  studentIdSchema,
  qrGenerationSchema,
  type QRGenerationInput
} from '@/lib/validations/student'
import { qrCodeUtils } from '@/lib/utils/student'

/**
 * POST /api/students/[id]/qr-generate
 * Generate QR code for a specific student
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'qr-generate', 20, 60 * 1000) // 20 generations per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can generate QR codes)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Parse and validate request body
    const body = await request.json().catch(() => ({}))
    const validationResult = qrGenerationSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const qrOptions: QRGenerationInput = validationResult.data

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        status: true
      }
    })

    if (!student) {
      return NextResponse.json(
        { success: false, error: 'Student not found' },
        { status: 404 }
      )
    }

    // Check if student is active
    if (student.status !== 'ACTIVE') {
      return NextResponse.json(
        { success: false, error: 'Cannot generate QR code for inactive student' },
        { status: 400 }
      )
    }

    // Generate QR code
    const qrCode = await qrCodeUtils.generateForStudent(student.id, qrOptions.expiryHours)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'qr_code',
      resourceId: qrCode.id,
      details: {
        studentId: student.id,
        studentNumber: student.studentNumber,
        studentName: `${student.firstName} ${student.lastName}`,
        expiryHours: qrOptions.expiryHours,
        qrData: qrCode.qrData
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        id: qrCode.id,
        qrData: qrCode.qrData,
        generatedAt: qrCode.generatedAt,
        expiresAt: qrCode.expiresAt,
        isActive: qrCode.isActive,
        student: qrCode.student
      },
      message: 'QR code generated successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/students/[id]/qr-generate error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}