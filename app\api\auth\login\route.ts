import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { authenticate, cookieUtils } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'

// Login request schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional().default(false)
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'login', 5, 15 * 60 * 1000) // 5 attempts per 15 minutes
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many login attempts. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = loginSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { username, password, rememberMe } = validationResult.data

    // Authenticate user
    const authResult = await authenticate(username, password)

    if (!authResult.success) {
      // Log failed login attempt
      if (authResult.user?.id) {
        await auditHelpers.login(authResult.user.id, request, false)
      }

      return NextResponse.json(
        { 
          success: false, 
          error: authResult.error || 'Authentication failed'
        },
        { status: 401 }
      )
    }

    // Log successful login
    await auditHelpers.login(authResult.user!.id, request, true)

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      user: {
        id: authResult.user!.id,
        username: authResult.user!.username,
        email: authResult.user!.email,
        firstName: authResult.user!.firstName,
        lastName: authResult.user!.lastName,
        role: authResult.user!.role,
        lastLoginAt: authResult.user!.lastLoginAt
      }
    })

    // Set authentication cookies
    if (authResult.accessToken && authResult.refreshToken) {
      // Access token cookie (short-lived)
      response.cookies.set('access-token', authResult.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 15 * 60, // 15 minutes
        path: '/'
      })

      // Refresh token cookie (longer-lived, adjust based on rememberMe)
      const refreshMaxAge = rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60 // 30 days or 7 days
      response.cookies.set('refresh-token', authResult.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: refreshMaxAge,
        path: '/'
      })
    }

    return response

  } catch (error) {
    console.error('Login API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
