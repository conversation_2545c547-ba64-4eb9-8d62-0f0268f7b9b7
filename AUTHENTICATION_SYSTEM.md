# Authentication System - QR Student Attendance System

## Overview

This document describes the comprehensive JWT-based authentication system implemented for the QR-Code Based Student Attendance and Monitoring System (QRSAMS) for Tanauan School of Arts and Trade.

## Features

### 🔐 Core Authentication
- **JWT-based Authentication** with httpOnly cookies
- **Refresh Token System** for seamless session management
- **Role-based Access Control** (<PERSON><PERSON>, Teacher, Staff)
- **Password Hashing** using bcryptjs with configurable rounds
- **Session Management** with automatic logout and token refresh

### 🛡️ Security Features
- **Rate Limiting** for login attempts and API calls
- **Account Lockout** after failed login attempts
- **CSRF Protection** for state-changing operations
- **Input Validation** and sanitization
- **SQL Injection** and XSS prevention
- **Security Headers** (CSP, HSTS, etc.)
- **IP-based Security Tracking**

### 📊 Audit & Monitoring
- **Comprehensive Audit Logging** for all user actions
- **Login Attempt Tracking** with IP monitoring
- **Suspicious Activity Detection**
- **Security Statistics** and reporting

## API Endpoints

### Authentication Routes

#### POST `/api/auth/login`
Login with username/email and password.

**Request Body:**
```json
{
  "username": "admin",
  "password": "password123",
  "rememberMe": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "user_id",
    "username": "admin",
    "email": "<EMAIL>",
    "firstName": "System",
    "lastName": "Administrator",
    "role": "ADMIN",
    "lastLoginAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### POST `/api/auth/logout`
Logout and clear authentication cookies.

#### GET `/api/auth/me`
Get current user profile information.

#### POST `/api/auth/refresh`
Refresh access token using refresh token.

#### POST `/api/auth/forgot-password`
Request password reset token.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST `/api/auth/reset-password`
Reset password using reset token.

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "new_password",
  "confirmPassword": "new_password"
}
```

## Middleware Protection

The authentication middleware automatically protects routes based on user roles:

### Protected Routes

#### Admin Only
- `/admin/*`
- `/api/admin/*`
- `/users/*`
- `/api/users/*`
- `/settings/*`
- `/api/settings/*`

#### Teacher & Admin
- `/students/*`
- `/api/students/*`
- `/attendance/*`
- `/api/attendance/*`
- `/classes/*`
- `/api/classes/*`

#### All Authenticated Users
- `/dashboard`
- `/` (redirects to dashboard)
- `/scan/*`
- `/reports/*`
- `/analytics/*`
- `/profile/*`

### Public Routes
- `/login`
- `/api/auth/*`
- `/api/health`
- Static files (`/_next/*`, `/images/*`, etc.)

## User Roles & Permissions

### ADMIN
- Full system access
- User management
- System settings
- All reports and analytics
- Student and teacher management

### TEACHER
- Student management
- Attendance management
- Class management
- Reports and analytics
- Profile management

### STAFF
- Basic attendance scanning
- Limited reports access
- Profile management

## Security Configuration

### Environment Variables

```env
# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-jwt-key"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME_MINUTES=15
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100
```

### Rate Limits

- **Login attempts**: 5 per 15 minutes per IP
- **Password reset**: 3 per hour per IP
- **General API**: 100 per 15 minutes per IP
- **File uploads**: 10 per hour per IP
- **Report generation**: 20 per hour per IP

### Password Requirements

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Not a common password

## Usage Examples

### React Components

```tsx
import { useAuth, usePermissions } from '@/hooks/use-auth'

function MyComponent() {
  const { user, isAuthenticated, logout } = useAuth()
  const { isAdmin, canManageStudents } = usePermissions()

  if (!isAuthenticated) {
    return <div>Please log in</div>
  }

  return (
    <div>
      <h1>Welcome, {user?.firstName}!</h1>
      {isAdmin && <AdminPanel />}
      {canManageStudents && <StudentManagement />}
      <button onClick={logout}>Logout</button>
    </div>
  )
}
```

### Protected Components

```tsx
import { withAuth } from '@/contexts/auth-context'
import { UserRole } from '@/generated/prisma'

const AdminComponent = withAuth(
  () => <div>Admin only content</div>,
  UserRole.ADMIN
)

const TeacherComponent = withAuth(
  () => <div>Teacher content</div>,
  [UserRole.ADMIN, UserRole.TEACHER]
)
```

### API Route Protection

```tsx
import { getUserFromToken } from '@/lib/auth'
import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  const userId = request.headers.get('x-user-id')
  const userRole = request.headers.get('x-user-role')
  
  // User info is automatically added by middleware
  // for authenticated requests
  
  return Response.json({ userId, userRole })
}
```

## Default Test Accounts

The system comes with pre-seeded test accounts:

| Username | Password | Role | Email |
|----------|----------|------|-------|
| admin | password123 | ADMIN | <EMAIL> |
| teacher1 | password123 | TEACHER | <EMAIL> |
| staff1 | password123 | STAFF | <EMAIL> |

## Security Best Practices

1. **Change default passwords** in production
2. **Use strong JWT secrets** (at least 32 characters)
3. **Enable HTTPS** in production
4. **Configure proper CORS** settings
5. **Monitor audit logs** regularly
6. **Implement proper backup** procedures
7. **Keep dependencies updated**

## Troubleshooting

### Common Issues

1. **"Invalid or expired token"**
   - Token may have expired
   - Try refreshing the page
   - Clear cookies and login again

2. **"Rate limit exceeded"**
   - Too many requests from same IP
   - Wait for the specified retry time
   - Check rate limit headers

3. **"Account locked"**
   - Too many failed login attempts
   - Wait for lockout period to expire
   - Contact administrator if needed

### Debug Mode

Set `NODE_ENV=development` to see additional logging and debug information.

## Audit Logging

All authentication events are logged with:
- User ID and username
- Action performed
- IP address and user agent
- Timestamp
- Additional context data

View audit logs through the admin interface or database queries.

## Future Enhancements

- [ ] Two-factor authentication (2FA)
- [ ] OAuth integration (Google, Microsoft)
- [ ] Single Sign-On (SSO)
- [ ] Advanced threat detection
- [ ] Email notifications for security events
- [ ] Mobile app authentication
- [ ] Biometric authentication support
