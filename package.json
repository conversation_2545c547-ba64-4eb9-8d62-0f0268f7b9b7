{"name": "qrsams", "version": "0.1.0", "private": true, "description": "QR-Code Based Student Attendance and Monitoring System for Tanauan School of Arts and Trade", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "preview": "next build && next start", "clean": "rm -rf .next out node_modules/.cache", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset && npm run db:seed"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.0.7", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.408.0", "next": "^14.2.31", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "prisma": "^6.13.0", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "recharts": "^3.1.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "tsx": "^4.20.3", "typescript": "^5.5.4"}}