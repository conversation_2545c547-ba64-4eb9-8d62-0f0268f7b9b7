import { z } from 'zod'
import { StudentStatus } from '../../generated/prisma'

// Base student schema for common fields
const baseStudentSchema = {
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'First name contains invalid characters'),

  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Last name contains invalid characters'),

  middleName: z.string()
    .max(50, 'Middle name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]*$/, 'Middle name contains invalid characters')
    .optional()
    .nullable(),

  gradeLevel: z.string()
    .min(1, 'Grade level is required')
    .max(20, 'Grade level must be less than 20 characters'),

  section: z.string()
    .min(1, 'Section is required')
    .max(20, 'Section must be less than 20 characters'),

  guardianName: z.string()
    .min(1, 'Guardian name is required')
    .max(100, 'Guardian name must be less than 100 characters'),

  guardianContact: z.string()
    .min(1, 'Guardian contact is required')
    .regex(/^(\+63|0)[0-9]{10}$/, 'Invalid Philippine phone number format'),

  address: z.string()
    .min(1, 'Address is required')
    .max(200, 'Address must be less than 200 characters'),

  status: z.nativeEnum(StudentStatus).optional().default(StudentStatus.ACTIVE)
}

// Create student schema
export const createStudentSchema = z.object({
  studentNumber: z.string()
    .min(1, 'Student number is required')
    .max(20, 'Student number must be less than 20 characters')
    .regex(/^[A-Z0-9\-]+$/, 'Student number must contain only uppercase letters, numbers, and hyphens'),

  ...baseStudentSchema
})

// Update student schema (all fields optional except ID)
export const updateStudentSchema = z.object({
  studentNumber: z.string()
    .min(1, 'Student number is required')
    .max(20, 'Student number must be less than 20 characters')
    .regex(/^[A-Z0-9\-]+$/, 'Student number must contain only uppercase letters, numbers, and hyphens')
    .optional(),

  firstName: baseStudentSchema.firstName.optional(),
  lastName: baseStudentSchema.lastName.optional(),
  middleName: baseStudentSchema.middleName.optional(),
  gradeLevel: baseStudentSchema.gradeLevel.optional(),
  section: baseStudentSchema.section.optional(),
  guardianName: baseStudentSchema.guardianName.optional(),
  guardianContact: baseStudentSchema.guardianContact.optional(),
  address: baseStudentSchema.address.optional(),
  status: baseStudentSchema.status.optional()
})

// Student query/filter schema
export const studentQuerySchema = z.object({
  // Pagination
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),

  // Search
  search: z.string().optional(),

  // Filters
  gradeLevel: z.string().optional(),
  section: z.string().optional(),
  status: z.nativeEnum(StudentStatus).optional(),

  // Sorting
  sortBy: z.enum([
    'studentNumber', 'firstName', 'lastName', 'gradeLevel',
    'section', 'dateEnrolled', 'createdAt', 'updatedAt'
  ]).default('lastName'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),

  // Include related data
  includeAttendance: z.coerce.boolean().default(false),
  includeQRCodes: z.coerce.boolean().default(false),
  includeSMSLogs: z.coerce.boolean().default(false)
})

// Bulk import schema
export const bulkImportSchema = z.object({
  students: z.array(createStudentSchema).min(1, 'At least one student is required'),
  skipDuplicates: z.boolean().default(true),
  updateExisting: z.boolean().default(false)
})

// Bulk operation schema
export const bulkOperationSchema = z.object({
  studentIds: z.array(z.string().cuid()).min(1, 'At least one student ID is required'),
  operation: z.enum(['delete', 'activate', 'deactivate', 'graduate', 'transfer']),
  reason: z.string().optional()
})

// QR code generation schema
export const qrGenerationSchema = z.object({
  expiryHours: z.number().min(1).max(8760).default(24), // Max 1 year
  batchGeneration: z.boolean().default(false),
  studentIds: z.array(z.string().cuid()).optional()
})

// Photo upload schema
export const photoUploadSchema = z.object({
  file: z.any(), // Will be validated separately for file type and size
  studentId: z.string().cuid()
})

// Export options schema
export const exportOptionsSchema = z.object({
  format: z.enum(['csv', 'excel', 'pdf']).default('csv'),
  fields: z.array(z.enum([
    'studentNumber', 'firstName', 'lastName', 'middleName',
    'gradeLevel', 'section', 'guardianName', 'guardianContact',
    'address', 'status', 'dateEnrolled', 'createdAt'
  ])).optional(),
  filters: studentQuerySchema.omit({
    page: true,
    limit: true,
    includeAttendance: true,
    includeQRCodes: true,
    includeSMSLogs: true
  }).optional(),
  includePhotos: z.boolean().default(false)
})

// Student ID parameter schema
export const studentIdSchema = z.object({
  id: z.string().cuid('Invalid student ID format')
})

// QR code lookup schema
export const qrLookupSchema = z.object({
  qrCode: z.string().min(1, 'QR code is required')
})

// Attendance summary schema for student details
export const attendanceSummarySchema = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  includeDetails: z.coerce.boolean().default(false)
})

// CSV import validation schema
export const csvImportSchema = z.object({
  file: z.any(), // File validation handled separately
  mapping: z.record(z.string(), z.string()).optional(), // Column mapping
  hasHeader: z.boolean().default(true),
  skipErrors: z.boolean().default(false)
})

// Student statistics schema
export const studentStatsSchema = z.object({
  gradeLevel: z.string().optional(),
  section: z.string().optional(),
  status: z.nativeEnum(StudentStatus).optional(),
  dateRange: z.object({
    start: z.coerce.date(),
    end: z.coerce.date()
  }).optional()
})

// Type exports for use in API routes
export type CreateStudentInput = z.infer<typeof createStudentSchema>
export type UpdateStudentInput = z.infer<typeof updateStudentSchema>
export type StudentQueryInput = z.infer<typeof studentQuerySchema>
export type BulkImportInput = z.infer<typeof bulkImportSchema>
export type BulkOperationInput = z.infer<typeof bulkOperationSchema>
export type QRGenerationInput = z.infer<typeof qrGenerationSchema>
export type PhotoUploadInput = z.infer<typeof photoUploadSchema>
export type ExportOptionsInput = z.infer<typeof exportOptionsSchema>
export type StudentIdInput = z.infer<typeof studentIdSchema>
export type QRLookupInput = z.infer<typeof qrLookupSchema>
export type AttendanceSummaryInput = z.infer<typeof attendanceSummarySchema>
export type CSVImportInput = z.infer<typeof csvImportSchema>
export type StudentStatsInput = z.infer<typeof studentStatsSchema>

// Validation helper functions
export const validateStudentNumber = async (studentNumber: string, excludeId?: string) => {
  // This would typically check against the database
  // Implementation will be in the utility functions
  return true
}

export const validateGradeSection = (gradeLevel: string, section: string) => {
  // Validate grade level and section combination
  const validGrades = ['K1', 'K2', 'G1', 'G2', 'G3', 'G4', 'G5', 'G6', 'G7', 'G8', 'G9', 'G10', 'G11', 'G12']
  const validSections = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']

  return validGrades.includes(gradeLevel) && validSections.includes(section)
}

// Error messages
export const studentValidationErrors = {
  STUDENT_NUMBER_EXISTS: 'Student number already exists',
  INVALID_GRADE_SECTION: 'Invalid grade level and section combination',
  GUARDIAN_CONTACT_INVALID: 'Invalid guardian contact number format',
  FILE_TOO_LARGE: 'File size exceeds maximum limit',
  INVALID_FILE_TYPE: 'Invalid file type',
  BULK_LIMIT_EXCEEDED: 'Bulk operation limit exceeded'
} as const