import { NextRequest } from 'next/server'

// In-memory store for rate limiting (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Clean up expired entries periodically
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 60000) // Clean up every minute

// Get client identifier from request
const getClientId = (request: NextRequest): string => {
  // Try to get IP address from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown'
  
  return ip
}

// Rate limiting function
export interface RateLimitResult {
  success: boolean
  limit: number
  remaining: number
  resetTime: number
  retryAfter?: number
}

export const rateLimit = async (
  request: NextRequest,
  identifier: string = 'default',
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes default
): Promise<RateLimitResult> => {
  const clientId = getClientId(request)
  const key = `${identifier}:${clientId}`
  const now = Date.now()
  const resetTime = now + windowMs

  // Get current rate limit data
  const current = rateLimitStore.get(key)

  if (!current || now > current.resetTime) {
    // First request or window expired, reset counter
    rateLimitStore.set(key, { count: 1, resetTime })
    return {
      success: true,
      limit: maxRequests,
      remaining: maxRequests - 1,
      resetTime
    }
  }

  if (current.count >= maxRequests) {
    // Rate limit exceeded
    return {
      success: false,
      limit: maxRequests,
      remaining: 0,
      resetTime: current.resetTime,
      retryAfter: Math.ceil((current.resetTime - now) / 1000)
    }
  }

  // Increment counter
  current.count++
  rateLimitStore.set(key, current)

  return {
    success: true,
    limit: maxRequests,
    remaining: maxRequests - current.count,
    resetTime: current.resetTime
  }
}

// Specific rate limiters for different endpoints
export const rateLimiters = {
  // Login attempts: 5 per 15 minutes
  login: (request: NextRequest) => 
    rateLimit(request, 'login', 5, 15 * 60 * 1000),

  // Password reset: 3 per hour
  passwordReset: (request: NextRequest) => 
    rateLimit(request, 'password-reset', 3, 60 * 60 * 1000),

  // General API: 100 per 15 minutes
  api: (request: NextRequest) => 
    rateLimit(request, 'api', 100, 15 * 60 * 1000),

  // File uploads: 10 per hour
  upload: (request: NextRequest) => 
    rateLimit(request, 'upload', 10, 60 * 60 * 1000),

  // Report generation: 20 per hour
  reports: (request: NextRequest) => 
    rateLimit(request, 'reports', 20, 60 * 60 * 1000)
}

// Middleware helper to add rate limit headers
export const addRateLimitHeaders = (
  response: Response,
  rateLimitResult: RateLimitResult
): Response => {
  const headers = new Headers(response.headers)
  
  headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString())
  headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString())
  headers.set('X-RateLimit-Reset', new Date(rateLimitResult.resetTime).toISOString())
  
  if (rateLimitResult.retryAfter) {
    headers.set('Retry-After', rateLimitResult.retryAfter.toString())
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  })
}

// Rate limit middleware for API routes
export const withRateLimit = (
  handler: (request: NextRequest) => Promise<Response>,
  limiter: (request: NextRequest) => Promise<RateLimitResult> = rateLimiters.api
) => {
  return async (request: NextRequest): Promise<Response> => {
    const rateLimitResult = await limiter(request)
    
    if (!rateLimitResult.success) {
      const response = new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      
      return addRateLimitHeaders(response, rateLimitResult)
    }

    const response = await handler(request)
    return addRateLimitHeaders(response, rateLimitResult)
  }
}

// Clear rate limit for a specific client and identifier
export const clearRateLimit = (clientId: string, identifier: string = 'default'): void => {
  const key = `${identifier}:${clientId}`
  rateLimitStore.delete(key)
}

// Get current rate limit status
export const getRateLimitStatus = (
  clientId: string, 
  identifier: string = 'default'
): { count: number; resetTime: number } | null => {
  const key = `${identifier}:${clientId}`
  return rateLimitStore.get(key) || null
}

// Reset all rate limits (useful for testing)
export const resetAllRateLimits = (): void => {
  rateLimitStore.clear()
}
