import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { passwordUtils } from '@/lib/auth'
import { withRateLimit, rateLimiters } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'
import { validatePassword, getClientIP } from '@/lib/security'

// Reset password request schema
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters long'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

async function handler(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = resetPasswordSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { token, password } = validationResult.data

    // Validate password strength
    const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password does not meet security requirements',
          details: passwordValidation.errors
        },
        { status: 400 }
      )
    }

    // Find user with valid reset token
    const user = await prisma.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          gt: new Date()
        },
        isActive: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired reset token' 
        },
        { status: 400 }
      )
    }

    // Hash new password
    const hashedPassword = await passwordUtils.hash(password)

    // Update user password and clear reset token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordHash: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
        // Reset login attempts on successful password reset
        loginAttempts: 0,
        lockedUntil: null
      }
    })

    // Log password reset completion
    await auditHelpers.createAuditLog({
      userId: user.id,
      action: 'PASSWORD_RESET_COMPLETE' as any,
      resource: 'authentication',
      details: { 
        email: user.email,
        passwordStrength: passwordValidation.score
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    return NextResponse.json({
      success: true,
      message: 'Password has been reset successfully. You can now log in with your new password.'
    })

  } catch (error) {
    console.error('Reset password API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// Apply rate limiting
export const POST = withRateLimit(handler, rateLimiters.passwordReset)

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
