import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import { exportOptionsSchema } from '@/lib/validations/student'
import { studentDbUtils, csvUtils } from '@/lib/utils/student'

/**
 * GET /api/students/export
 * Export student data in various formats
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'student-export', 5, 60 * 1000) // 5 exports per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can export)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = exportOptionsSchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid export parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const exportOptions = validationResult.data

    // Build query filters
    const where = exportOptions.filters ? studentDbUtils.buildWhereClause(exportOptions.filters) : {}

    // Get students data
    const students = await prisma.student.findMany({
      where,
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' }
      ],
      ...(exportOptions.includePhotos && {
        select: {
          id: true,
          studentNumber: true,
          firstName: true,
          lastName: true,
          middleName: true,
          gradeLevel: true,
          section: true,
          guardianName: true,
          guardianContact: true,
          address: true,
          profilePhoto: true,
          status: true,
          dateEnrolled: true,
          createdAt: true,
          updatedAt: true
        }
      })
    })

    if (students.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No students found matching the criteria' },
        { status: 404 }
      )
    }

    // Limit export size
    if (students.length > 5000) {
      return NextResponse.json(
        { success: false, error: 'Too many records to export. Please apply filters to reduce the dataset.' },
        { status: 400 }
      )
    }

    // Generate export based on format
    let exportData: string | Buffer
    let contentType: string
    let filename: string

    switch (exportOptions.format) {
      case 'csv':
        exportData = csvUtils.convertToCSV(students, exportOptions.fields)
        contentType = 'text/csv'
        filename = `students_export_${new Date().toISOString().split('T')[0]}.csv`
        break

      case 'excel':
        // For now, return CSV with Excel content type
        // In a real implementation, you'd use a library like xlsx
        exportData = csvUtils.convertToCSV(students, exportOptions.fields)
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        filename = `students_export_${new Date().toISOString().split('T')[0]}.xlsx`
        break

      case 'pdf':
        // For now, return JSON data
        // In a real implementation, you'd use a PDF generation library
        exportData = JSON.stringify(students, null, 2)
        contentType = 'application/pdf'
        filename = `students_export_${new Date().toISOString().split('T')[0]}.pdf`
        break

      default:
        exportData = csvUtils.convertToCSV(students, exportOptions.fields)
        contentType = 'text/csv'
        filename = `students_export_${new Date().toISOString().split('T')[0]}.csv`
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'EXPORT',
      resource: 'students',
      details: {
        format: exportOptions.format,
        recordCount: students.length,
        fields: exportOptions.fields,
        filters: exportOptions.filters,
        includePhotos: exportOptions.includePhotos
      }
    }, request)

    // Return file as download
    const response = new NextResponse(exportData, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': Buffer.byteLength(exportData).toString()
      }
    })

    return response

  } catch (error) {
    console.error('GET /api/students/export error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}