import { NextRequest, NextResponse } from 'next/server'
import { cookieUtils, getUserFromToken } from '@/lib/auth'
import { withRateLimit, rateLimiters } from '@/lib/rate-limit'

async function handler(request: NextRequest) {
  try {
    // Get token from request
    const token = cookieUtils.getTokenFromRequest(request)
    
    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No authentication token provided' 
        },
        { status: 401 }
      )
    }

    // Get user from token
    const user = await getUserFromToken(token)
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired token' 
        },
        { status: 401 }
      )
    }

    // Return user data
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        lastLoginAt: user.lastLoginAt
      }
    })

  } catch (error) {
    console.error('Get user profile API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// Apply rate limiting to the handler
export const GET = withRateLimit(handler, rateLimiters.api)

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
