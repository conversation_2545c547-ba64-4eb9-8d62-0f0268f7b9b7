import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'

// Error types for better error handling
export class StudentAPIError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'StudentAPIError'
  }
}

export class ValidationError extends StudentAPIError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends StudentAPIError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR')
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends StudentAPIError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR')
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends StudentAPIError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR')
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends StudentAPIError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT_ERROR', details)
    this.name = 'ConflictError'
  }
}

export class RateLimitError extends StudentAPIError {
  constructor(message: string, retryAfter?: number) {
    super(message, 429, 'RATE_LIMIT_ERROR', { retryAfter })
    this.name = 'RateLimitError'
  }
}

// Authentication middleware
export async function authenticateRequest(request: NextRequest) {
  const token = request.cookies.get('access-token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '')

  if (!token) {
    throw new AuthenticationError()
  }

  const user = await getUserFromToken(token)
  if (!user) {
    throw new AuthenticationError('Invalid or expired token')
  }

  return user
}

// Authorization middleware
export function authorizeRoles(allowedRoles: string[]) {
  return (user: any) => {
    if (!allowedRoles.includes(user.role)) {
      throw new AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`)
    }
  }
}

// Rate limiting middleware
export async function applyRateLimit(
  request: NextRequest,
  key: string,
  limit: number,
  windowMs: number
) {
  const rateLimitResult = await rateLimit(request, key, limit, windowMs)
  if (!rateLimitResult.success) {
    throw new RateLimitError(
      'Too many requests. Please try again later.',
      rateLimitResult.retryAfter
    )
  }
}

// Error response formatter
export function formatErrorResponse(error: any) {
  console.error('API Error:', error)

  // Handle known error types
  if (error instanceof StudentAPIError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
        ...(error.details && { details: error.details })
      },
      { status: error.statusCode }
    )
  }

  // Handle Prisma errors
  if (error.code === 'P2002') {
    return NextResponse.json(
      {
        success: false,
        error: 'A record with this information already exists',
        code: 'DUPLICATE_RECORD',
        field: error.meta?.target?.[0]
      },
      { status: 409 }
    )
  }

  if (error.code === 'P2025') {
    return NextResponse.json(
      {
        success: false,
        error: 'Record not found',
        code: 'RECORD_NOT_FOUND'
      },
      { status: 404 }
    )
  }

  // Handle validation errors from Zod
  if (error.name === 'ZodError') {
    return NextResponse.json(
      {
        success: false,
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: error.errors
      },
      { status: 400 }
    )
  }

  // Handle file upload errors
  if (error.message?.includes('file') || error.message?.includes('upload')) {
    return NextResponse.json(
      {
        success: false,
        error: 'File upload failed',
        code: 'FILE_UPLOAD_ERROR',
        details: error.message
      },
      { status: 400 }
    )
  }

  // Generic server error
  return NextResponse.json(
    {
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    },
    { status: 500 }
  )
}

// API wrapper for consistent error handling
export function withErrorHandling(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    try {
      return await handler(request, context)
    } catch (error) {
      return formatErrorResponse(error)
    }
  }
}

// Audit logging wrapper
export function withAuditLogging(
  action: string,
  resource: string,
  handler: Function
) {
  return async (request: NextRequest, context?: any) => {
    const startTime = Date.now()
    let user: any = null
    let error: any = null
    let result: any = null

    try {
      // Try to get user for audit logging
      try {
        user = await authenticateRequest(request)
      } catch (authError) {
        // Continue without user for audit logging
      }

      result = await handler(request, context)
      return result
    } catch (handlerError) {
      error = handlerError
      throw handlerError
    } finally {
      // Log audit trail
      if (user) {
        try {
          await auditHelpers.log({
            userId: user.id,
            action: action as any,
            resource,
            resourceId: context?.params?.id,
            details: {
              method: request.method,
              url: request.url,
              duration: Date.now() - startTime,
              success: !error,
              error: error?.message
            }
          }, request)
        } catch (auditError) {
          console.error('Audit logging failed:', auditError)
        }
      }
    }
  }
}

// Combined middleware wrapper
export function withMiddleware(
  options: {
    rateLimit?: { key: string; limit: number; windowMs: number }
    auth?: boolean
    roles?: string[]
    audit?: { action: string; resource: string }
  }
) {
  return (handler: Function) => {
    return withErrorHandling(async (request: NextRequest, context?: any) => {
      // Apply rate limiting
      if (options.rateLimit) {
        await applyRateLimit(
          request,
          options.rateLimit.key,
          options.rateLimit.limit,
          options.rateLimit.windowMs
        )
      }

      // Apply authentication
      let user: any = null
      if (options.auth) {
        user = await authenticateRequest(request)
      }

      // Apply authorization
      if (options.roles && user) {
        authorizeRoles(options.roles)(user)
      }

      // Execute handler with audit logging
      if (options.audit) {
        return await withAuditLogging(
          options.audit.action,
          options.audit.resource,
          handler
        )(request, context)
      }

      return await handler(request, context)
    })
  }
}