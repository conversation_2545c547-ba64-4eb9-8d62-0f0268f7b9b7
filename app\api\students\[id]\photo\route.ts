import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import { studentIdSchema } from '@/lib/validations/student'
import { fileUtils } from '@/lib/utils/student'
import path from 'path'

/**
 * POST /api/students/[id]/photo
 * Upload student profile photo
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'photo-upload', 10, 60 * 1000) // 10 uploads per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can upload photos)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        profilePhoto: true
      }
    })

    if (!student) {
      return NextResponse.json(
        { success: false, error: 'Student not found' },
        { status: 404 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('photo') as File

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No photo file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!fileUtils.validateFileType(file, allowedTypes)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    if (!fileUtils.validateFileSize(file, 5)) {
      return NextResponse.json(
        { success: false, error: 'File size too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Create upload directory path
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'students', 'photos')

    try {
      // Save the uploaded file
      const savedFile = await fileUtils.saveUploadedFile(file, uploadDir)

      // Delete old photo if exists
      if (student.profilePhoto) {
        const oldPhotoPath = path.join(process.cwd(), 'public', student.profilePhoto)
        await fileUtils.deleteFile(oldPhotoPath)
      }

      // Update student record with new photo path
      const photoPath = `/uploads/students/photos/${savedFile.filename}`
      const updatedStudent = await prisma.student.update({
        where: { id: params.id },
        data: { profilePhoto: photoPath }
      })

      // Log audit trail
      await auditHelpers.log({
        userId: user.id,
        action: 'UPDATE',
        resource: 'student_photo',
        resourceId: student.id,
        details: {
          studentId: student.id,
          studentNumber: student.studentNumber,
          studentName: `${student.firstName} ${student.lastName}`,
          photoPath: photoPath,
          fileSize: savedFile.size,
          fileType: savedFile.type
        }
      }, request)

      return NextResponse.json({
        success: true,
        data: {
          photoUrl: photoPath,
          filename: savedFile.filename,
          size: savedFile.size,
          type: savedFile.type
        },
        message: 'Photo uploaded successfully'
      }, { status: 201 })

    } catch (fileError) {
      console.error('File upload error:', fileError)
      return NextResponse.json(
        { success: false, error: 'Failed to save photo file' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('POST /api/students/[id]/photo error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/students/[id]/photo
 * Delete student profile photo
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'photo-delete', 20, 60 * 1000) // 20 deletes per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can delete photos)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Check if student exists and has a photo
    const student = await prisma.student.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        profilePhoto: true
      }
    })

    if (!student) {
      return NextResponse.json(
        { success: false, error: 'Student not found' },
        { status: 404 }
      )
    }

    if (!student.profilePhoto) {
      return NextResponse.json(
        { success: false, error: 'Student has no profile photo' },
        { status: 400 }
      )
    }

    try {
      // Delete the photo file
      const photoPath = path.join(process.cwd(), 'public', student.profilePhoto)
      await fileUtils.deleteFile(photoPath)

      // Update student record to remove photo reference
      await prisma.student.update({
        where: { id: params.id },
        data: { profilePhoto: null }
      })

      // Log audit trail
      await auditHelpers.log({
        userId: user.id,
        action: 'DELETE',
        resource: 'student_photo',
        resourceId: student.id,
        details: {
          studentId: student.id,
          studentNumber: student.studentNumber,
          studentName: `${student.firstName} ${student.lastName}`,
          deletedPhotoPath: student.profilePhoto
        }
      }, request)

      return NextResponse.json({
        success: true,
        message: 'Photo deleted successfully'
      })

    } catch (fileError) {
      console.error('File deletion error:', fileError)
      return NextResponse.json(
        { success: false, error: 'Failed to delete photo file' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('DELETE /api/students/[id]/photo error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}