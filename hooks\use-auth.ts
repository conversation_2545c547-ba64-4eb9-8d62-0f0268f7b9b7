"use client"

import { useAuth as useAuthContext, User } from '@/contexts/auth-context'
import { UserRole } from '../generated/prisma'
import { useState, useCallback } from 'react'

// Re-export the main auth hook
export const useAuth = useAuthContext

// Login form state interface
interface LoginState {
  isLoading: boolean
  error: string | null
  success: boolean
}

// Enhanced login hook with form state management
export const useLogin = () => {
  const { login } = useAuth()
  const [state, setState] = useState<LoginState>({
    isLoading: false,
    error: null,
    success: false
  })

  const handleLogin = useCallback(async (
    username: string, 
    password: string, 
    rememberMe: boolean = false
  ) => {
    setState({ isLoading: true, error: null, success: false })
    
    try {
      const result = await login(username, password, rememberMe)
      
      if (result.success) {
        setState({ isLoading: false, error: null, success: true })
        return { success: true, user: result.user }
      } else {
        setState({ isLoading: false, error: result.error || 'Login failed', success: false })
        return { success: false, error: result.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setState({ isLoading: false, error: errorMessage, success: false })
      return { success: false, error: errorMessage }
    }
  }, [login])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  const reset = useCallback(() => {
    setState({ isLoading: false, error: null, success: false })
  }, [])

  return {
    ...state,
    login: handleLogin,
    clearError,
    reset
  }
}

// Permission checking hooks
export const usePermissions = () => {
  const { user } = useAuth()

  const hasRole = useCallback((role: UserRole | UserRole[]): boolean => {
    if (!user) return false
    
    if (Array.isArray(role)) {
      return role.includes(user.role)
    }
    
    return user.role === role
  }, [user])

  const isAdmin = useCallback((): boolean => {
    return hasRole(UserRole.ADMIN)
  }, [hasRole])

  const isTeacher = useCallback((): boolean => {
    return hasRole([UserRole.ADMIN, UserRole.TEACHER])
  }, [hasRole])

  const isStaff = useCallback((): boolean => {
    return hasRole([UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF])
  }, [hasRole])

  const canManageUsers = useCallback((): boolean => {
    return isAdmin()
  }, [isAdmin])

  const canManageStudents = useCallback((): boolean => {
    return hasRole([UserRole.ADMIN, UserRole.TEACHER])
  }, [hasRole])

  const canViewReports = useCallback((): boolean => {
    return hasRole([UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF])
  }, [hasRole])

  const canManageAttendance = useCallback((): boolean => {
    return hasRole([UserRole.ADMIN, UserRole.TEACHER])
  }, [hasRole])

  const canAccessSettings = useCallback((): boolean => {
    return isAdmin()
  }, [isAdmin])

  return {
    user,
    hasRole,
    isAdmin,
    isTeacher,
    isStaff,
    canManageUsers,
    canManageStudents,
    canViewReports,
    canManageAttendance,
    canAccessSettings
  }
}

// Session management hook
export const useSession = () => {
  const { user, isAuthenticated, isLoading, logout, refreshToken, checkAuth } = useAuth()
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      const success = await refreshToken()
      if (success) {
        await checkAuth() // Refresh user data
      }
      return success
    } finally {
      setIsRefreshing(false)
    }
  }, [refreshToken, checkAuth])

  const handleLogout = useCallback(async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
      // Force redirect even if logout API fails
      window.location.href = '/login'
    }
  }, [logout])

  return {
    user,
    isAuthenticated,
    isLoading,
    isRefreshing,
    logout: handleLogout,
    refreshToken: handleRefresh,
    checkAuth
  }
}

// User profile hook
export const useUserProfile = () => {
  const { user } = useAuth()

  const getFullName = useCallback((): string => {
    if (!user) return ''
    return `${user.firstName} ${user.lastName}`.trim()
  }, [user])

  const getInitials = useCallback((): string => {
    if (!user) return ''
    const firstInitial = user.firstName.charAt(0).toUpperCase()
    const lastInitial = user.lastName.charAt(0).toUpperCase()
    return `${firstInitial}${lastInitial}`
  }, [user])

  const getRoleDisplayName = useCallback((): string => {
    if (!user) return ''
    
    switch (user.role) {
      case UserRole.ADMIN:
        return 'Administrator'
      case UserRole.TEACHER:
        return 'Teacher'
      case UserRole.STAFF:
        return 'Staff'
      default:
        return 'User'
    }
  }, [user])

  const getLastLoginDisplay = useCallback((): string => {
    if (!user?.lastLoginAt) return 'Never'
    
    const lastLogin = new Date(user.lastLoginAt)
    const now = new Date()
    const diffMs = now.getTime() - lastLogin.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} minutes ago`
    if (diffHours < 24) return `${diffHours} hours ago`
    if (diffDays < 7) return `${diffDays} days ago`
    
    return lastLogin.toLocaleDateString()
  }, [user])

  return {
    user,
    fullName: getFullName(),
    initials: getInitials(),
    roleDisplayName: getRoleDisplayName(),
    lastLoginDisplay: getLastLoginDisplay()
  }
}

// Authentication guard hook for components
export const useAuthGuard = (requiredRole?: UserRole | UserRole[]) => {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { hasRole } = usePermissions()

  const isAuthorized = useCallback((): boolean => {
    if (!isAuthenticated || !user) return false
    if (!requiredRole) return true
    return hasRole(requiredRole)
  }, [isAuthenticated, user, requiredRole, hasRole])

  const getAuthStatus = useCallback(() => {
    if (isLoading) return 'loading'
    if (!isAuthenticated) return 'unauthenticated'
    if (!isAuthorized()) return 'unauthorized'
    return 'authorized'
  }, [isLoading, isAuthenticated, isAuthorized])

  return {
    user,
    isLoading,
    isAuthenticated,
    isAuthorized: isAuthorized(),
    authStatus: getAuthStatus()
  }
}

// Auto-logout hook for inactivity
export const useAutoLogout = (timeoutMinutes: number = 30) => {
  const { logout, isAuthenticated } = useAuth()
  const [lastActivity, setLastActivity] = useState(Date.now())
  const [showWarning, setShowWarning] = useState(false)

  const resetActivity = useCallback(() => {
    setLastActivity(Date.now())
    setShowWarning(false)
  }, [])

  const handleLogout = useCallback(async () => {
    await logout()
  }, [logout])

  // Track user activity
  useCallback(() => {
    if (!isAuthenticated) return

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    
    const activityHandler = () => resetActivity()
    
    events.forEach(event => {
      document.addEventListener(event, activityHandler, true)
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, activityHandler, true)
      })
    }
  }, [isAuthenticated, resetActivity])

  // Check for inactivity
  useCallback(() => {
    if (!isAuthenticated) return

    const checkInactivity = () => {
      const now = Date.now()
      const timeSinceLastActivity = now - lastActivity
      const timeoutMs = timeoutMinutes * 60 * 1000
      const warningMs = timeoutMs - (5 * 60 * 1000) // Show warning 5 minutes before logout

      if (timeSinceLastActivity >= timeoutMs) {
        handleLogout()
      } else if (timeSinceLastActivity >= warningMs && !showWarning) {
        setShowWarning(true)
      }
    }

    const interval = setInterval(checkInactivity, 60000) // Check every minute
    return () => clearInterval(interval)
  }, [isAuthenticated, lastActivity, timeoutMinutes, showWarning, handleLogout])

  return {
    showWarning,
    resetActivity,
    timeRemaining: Math.max(0, (timeoutMinutes * 60 * 1000) - (Date.now() - lastActivity))
  }
}
