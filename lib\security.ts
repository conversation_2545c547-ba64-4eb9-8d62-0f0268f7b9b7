import { NextRequest } from 'next/server'
import crypto from 'crypto'

// Security configuration
const SECURITY_CONFIG = {
  maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
  lockoutTimeMinutes: parseInt(process.env.LOCKOUT_TIME_MINUTES || '15'),
  sessionTimeoutMinutes: parseInt(process.env.SESSION_TIMEOUT_MINUTES || '30'),
  passwordMinLength: 8,
  passwordRequireUppercase: true,
  passwordRequireLowercase: true,
  passwordRequireNumbers: true,
  passwordRequireSpecialChars: true
}

// IP-based security tracking
const securityStore = new Map<string, {
  attempts: number
  lastAttempt: number
  lockedUntil?: number
  suspiciousActivity: number
}>()

// Clean up expired entries
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of securityStore.entries()) {
    if (value.lockedUntil && now > value.lockedUntil) {
      securityStore.delete(key)
    }
  }
}, 60000) // Clean up every minute

// Get client IP address
export const getClientIP = (request: NextRequest): string => {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown'
  return ip
}

// Get client fingerprint for additional security
export const getClientFingerprint = (request: NextRequest): string => {
  const userAgent = request.headers.get('user-agent') || ''
  const acceptLanguage = request.headers.get('accept-language') || ''
  const acceptEncoding = request.headers.get('accept-encoding') || ''
  
  const fingerprint = `${userAgent}|${acceptLanguage}|${acceptEncoding}`
  return crypto.createHash('sha256').update(fingerprint).digest('hex').substring(0, 16)
}

// Check if IP is currently locked out
export const isIPLocked = (ip: string): { locked: boolean; retryAfter?: number } => {
  const security = securityStore.get(ip)
  if (!security || !security.lockedUntil) {
    return { locked: false }
  }

  const now = Date.now()
  if (now > security.lockedUntil) {
    // Lockout expired, clean up
    securityStore.delete(ip)
    return { locked: false }
  }

  return {
    locked: true,
    retryAfter: Math.ceil((security.lockedUntil - now) / 1000)
  }
}

// Record failed login attempt
export const recordFailedAttempt = (ip: string): void => {
  const now = Date.now()
  const security = securityStore.get(ip) || {
    attempts: 0,
    lastAttempt: 0,
    suspiciousActivity: 0
  }

  security.attempts++
  security.lastAttempt = now

  // Check if we should lock the IP
  if (security.attempts >= SECURITY_CONFIG.maxLoginAttempts) {
    security.lockedUntil = now + (SECURITY_CONFIG.lockoutTimeMinutes * 60 * 1000)
  }

  securityStore.set(ip, security)
}

// Record successful login (reset attempts)
export const recordSuccessfulLogin = (ip: string): void => {
  securityStore.delete(ip)
}

// Record suspicious activity
export const recordSuspiciousActivity = (ip: string, activity: string): void => {
  const security = securityStore.get(ip) || {
    attempts: 0,
    lastAttempt: 0,
    suspiciousActivity: 0
  }

  security.suspiciousActivity++
  
  // If too much suspicious activity, extend lockout
  if (security.suspiciousActivity >= 3) {
    const now = Date.now()
    security.lockedUntil = now + (60 * 60 * 1000) // 1 hour lockout
  }

  securityStore.set(ip, security)
  console.warn(`Suspicious activity from IP ${ip}: ${activity}`)
}

// Password strength validation
export interface PasswordValidation {
  isValid: boolean
  errors: string[]
  score: number // 0-100
}

export const validatePassword = (password: string): PasswordValidation => {
  const errors: string[] = []
  let score = 0

  // Length check
  if (password.length < SECURITY_CONFIG.passwordMinLength) {
    errors.push(`Password must be at least ${SECURITY_CONFIG.passwordMinLength} characters long`)
  } else {
    score += 20
  }

  // Uppercase check
  if (SECURITY_CONFIG.passwordRequireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  } else if (/[A-Z]/.test(password)) {
    score += 20
  }

  // Lowercase check
  if (SECURITY_CONFIG.passwordRequireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  } else if (/[a-z]/.test(password)) {
    score += 20
  }

  // Number check
  if (SECURITY_CONFIG.passwordRequireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  } else if (/\d/.test(password)) {
    score += 20
  }

  // Special character check
  if (SECURITY_CONFIG.passwordRequireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 20
  }

  // Additional scoring for length
  if (password.length >= 12) score += 10
  if (password.length >= 16) score += 10

  return {
    isValid: errors.length === 0,
    errors,
    score: Math.min(100, score)
  }
}

// Common password check (basic implementation)
const commonPasswords = [
  'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
  'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1'
]

export const isCommonPassword = (password: string): boolean => {
  return commonPasswords.includes(password.toLowerCase())
}

// Generate secure random token
export const generateSecureToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex')
}

// Hash sensitive data
export const hashData = (data: string): string => {
  return crypto.createHash('sha256').update(data).digest('hex')
}

// Timing-safe string comparison
export const safeCompare = (a: string, b: string): boolean => {
  if (a.length !== b.length) return false
  
  let result = 0
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i)
  }
  
  return result === 0
}

// Input sanitization
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .trim()
}

// SQL injection prevention (basic)
export const containsSQLInjection = (input: string): boolean => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(--|\/\*|\*\/)/,
    /(\b(OR|AND)\b.*=.*)/i,
    /(;|\||&)/
  ]
  
  return sqlPatterns.some(pattern => pattern.test(input))
}

// XSS prevention
export const containsXSS = (input: string): boolean => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[^>]*>/gi
  ]
  
  return xssPatterns.some(pattern => pattern.test(input))
}

// Request validation
export interface RequestValidation {
  isValid: boolean
  errors: string[]
  riskLevel: 'low' | 'medium' | 'high'
}

export const validateRequest = (request: NextRequest, body?: any): RequestValidation => {
  const errors: string[] = []
  let riskLevel: 'low' | 'medium' | 'high' = 'low'

  // Check for suspicious headers
  const userAgent = request.headers.get('user-agent') || ''
  if (!userAgent || userAgent.length < 10) {
    errors.push('Suspicious or missing user agent')
    riskLevel = 'medium'
  }

  // Check for common bot patterns
  const botPatterns = [
    /bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i
  ]
  
  if (botPatterns.some(pattern => pattern.test(userAgent))) {
    errors.push('Bot-like user agent detected')
    riskLevel = 'medium'
  }

  // Validate request body if provided
  if (body && typeof body === 'object') {
    for (const [key, value] of Object.entries(body)) {
      if (typeof value === 'string') {
        if (containsSQLInjection(value)) {
          errors.push(`Potential SQL injection in field: ${key}`)
          riskLevel = 'high'
        }
        
        if (containsXSS(value)) {
          errors.push(`Potential XSS in field: ${key}`)
          riskLevel = 'high'
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    riskLevel
  }
}

// Security headers for responses
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: blob:",
      "font-src 'self'",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests"
    ].join('; ')
  }
}

// Get security statistics
export const getSecurityStats = () => {
  const stats = {
    totalIPs: securityStore.size,
    lockedIPs: 0,
    suspiciousActivity: 0,
    totalAttempts: 0
  }

  for (const security of securityStore.values()) {
    if (security.lockedUntil && security.lockedUntil > Date.now()) {
      stats.lockedIPs++
    }
    stats.suspiciousActivity += security.suspiciousActivity
    stats.totalAttempts += security.attempts
  }

  return stats
}
