import { NextRequest, NextResponse } from 'next/server'
import { UserRole } from './generated/prisma'
import { jwtVerify } from 'jose'

// JWT Configuration
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'fallback-secret')

// JWT Payload Interface
interface JWTPayload {
  userId: string
  username: string
  email: string
  role: UserRole
  firstName: string
  lastName: string
  iat?: number
  exp?: number
}

// Get token from request
const getTokenFromRequest = (request: NextRequest): string | null => {
  // Try to get token from Authorization header first
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Fallback to cookie
  return request.cookies.get('access-token')?.value || null
}

// Verify JWT token using jose (Edge Runtime compatible)
const verifyToken = async (token: string): Promise<JWTPayload | null> => {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET, {
      issuer: 'qrsams',
      audience: 'qrsams-users'
    })

    // Validate that the payload has the required fields
    if (
      typeof payload.userId === 'string' &&
      typeof payload.username === 'string' &&
      typeof payload.email === 'string' &&
      typeof payload.role === 'string' &&
      typeof payload.firstName === 'string' &&
      typeof payload.lastName === 'string'
    ) {
      return payload as unknown as JWTPayload
    }

    return null
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

// Define protected routes and their required roles
const protectedRoutes = {
  // Admin only routes
  '/admin': [UserRole.ADMIN],
  '/api/admin': [UserRole.ADMIN],
  '/users': [UserRole.ADMIN],
  '/api/users': [UserRole.ADMIN],
  '/settings': [UserRole.ADMIN],
  '/api/settings': [UserRole.ADMIN],
  
  // Teacher and Admin routes
  '/students': [UserRole.ADMIN, UserRole.TEACHER],
  '/api/students': [UserRole.ADMIN, UserRole.TEACHER],
  '/attendance': [UserRole.ADMIN, UserRole.TEACHER],
  '/api/attendance': [UserRole.ADMIN, UserRole.TEACHER],
  '/classes': [UserRole.ADMIN, UserRole.TEACHER],
  '/api/classes': [UserRole.ADMIN, UserRole.TEACHER],
  
  // All authenticated users
  '/dashboard': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF], // Dashboard redirect
  '/scan': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/api/scan': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/reports': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/api/reports': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/analytics': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/api/analytics': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/profile': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF],
  '/api/profile': [UserRole.ADMIN, UserRole.TEACHER, UserRole.STAFF]
}

// Public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/api/auth/login',
  '/api/auth/refresh',
  '/api/health',
  '/favicon.ico',
  '/_next',
  '/images',
  '/sounds'
]

// Check if a route is public
const isPublicRoute = (pathname: string): boolean => {
  return publicRoutes.some(route => pathname.startsWith(route))
}

// Check if a route is protected and get required roles
const getRequiredRoles = (pathname: string): UserRole[] | null => {
  for (const [route, roles] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      return roles
    }
  }
  return null
}

// Generate CSRF token
const generateCSRFToken = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15)
}

// Verify CSRF token for state-changing operations
const verifyCSRFToken = (request: NextRequest): boolean => {
  const method = request.method
  
  // Only check CSRF for state-changing methods
  if (!['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
    return true
  }

  const tokenFromHeader = request.headers.get('x-csrf-token')
  const tokenFromCookie = request.cookies.get('csrf-token')?.value

  return tokenFromHeader === tokenFromCookie && tokenFromHeader !== undefined
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/_next/') ||
    pathname.includes('.') // Static files
  ) {
    return NextResponse.next()
  }

  // Allow public routes
  if (isPublicRoute(pathname)) {
    const response = NextResponse.next()
    
    // Set CSRF token for public routes that might need it
    if (!request.cookies.get('csrf-token')) {
      const csrfToken = generateCSRFToken()
      response.cookies.set('csrf-token', csrfToken, {
        httpOnly: false, // CSRF tokens need to be accessible to JavaScript
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60, // 24 hours
        path: '/'
      })
    }
    
    return response
  }

  // Get access token
  const accessToken = getTokenFromRequest(request)

  if (!accessToken) {
    // No token, redirect to login
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // Verify token
  const payload = await verifyToken(accessToken)

  if (!payload) {
    // Invalid token, redirect to login
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // Check role-based access
  const requiredRoles = getRequiredRoles(pathname)
  
  if (requiredRoles && !requiredRoles.includes(payload.role)) {
    // User doesn't have required role
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }

  // Verify CSRF token for API routes
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
    if (!verifyCSRFToken(request)) {
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403 }
      )
    }
  }

  // Create response with user context
  const response = NextResponse.next()
  
  // Add user information to headers for API routes
  response.headers.set('x-user-id', payload.userId)
  response.headers.set('x-user-role', payload.role)
  response.headers.set('x-user-username', payload.username)
  
  // Set/refresh CSRF token
  if (!request.cookies.get('csrf-token')) {
    const csrfToken = generateCSRFToken()
    response.cookies.set('csrf-token', csrfToken, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    })
  }

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  // Add CSP header for additional security
  const cspHeader = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: blob:",
    "font-src 'self'",
    "connect-src 'self'",
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; ')
  
  response.headers.set('Content-Security-Policy', cspHeader)

  return response
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
