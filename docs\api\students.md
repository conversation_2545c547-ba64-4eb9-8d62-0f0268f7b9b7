# Student Management API Documentation

## Overview

The Student Management API provides comprehensive CRUD operations for managing student records in the QR-Code Based Student Attendance and Monitoring System. All endpoints require authentication and implement role-based authorization.

## Authentication

All endpoints require authentication via:
- Cookie: `access-token`
- Header: `Authorization: Bearer <token>`

## Authorization Roles

- **ADMIN**: Full access to all operations
- **STAFF**: Can create, read, and update students; cannot delete
- **TEACHER**: Read-only access to student information

## Rate Limiting

All endpoints implement rate limiting to prevent abuse:
- List students: 100 requests/minute
- Individual operations: 50 requests/minute
- Bulk operations: 2-10 requests/minute
- File uploads: 10 requests/minute

## Base URL

```
/api/students
```

## Endpoints

### 1. List Students

**GET** `/api/students`

Retrieve a paginated list of students with advanced filtering and search capabilities.

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | number | 1 | Page number for pagination |
| `limit` | number | 20 | Number of records per page (max 100) |
| `search` | string | - | Search across name, student number, guardian name |
| `gradeLevel` | string | - | Filter by grade level |
| `section` | string | - | Filter by section |
| `status` | enum | - | Filter by status (ACTIVE, INACTIVE, GRADUATED, etc.) |
| `sortBy` | enum | lastName | Sort field (studentNumber, firstName, lastName, etc.) |
| `sortOrder` | enum | asc | Sort order (asc, desc) |
| `includeAttendance` | boolean | false | Include recent attendance records |
| `includeQRCodes` | boolean | false | Include active QR codes |
| `includeSMSLogs` | boolean | false | Include SMS notification logs |

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": "student_id",
      "studentNumber": "2024-001",
      "firstName": "John",
      "lastName": "Doe",
      "middleName": "Smith",
      "fullName": "John Smith Doe",
      "gradeLevel": "G10",
      "section": "A",
      "guardianName": "Jane Doe",
      "guardianContact": "+************",
      "address": "123 Main St, City",
      "profilePhoto": "/uploads/students/photos/photo.jpg",
      "status": "ACTIVE",
      "dateEnrolled": "2024-01-15T00:00:00.000Z",
      "createdAt": "2024-01-15T08:00:00.000Z",
      "updatedAt": "2024-01-15T08:00:00.000Z",
      "attendances": [],
      "qrCodes": [],
      "smsLogs": []
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPrevPage": false,
    "nextPage": 2,
    "prevPage": null
  },
  "meta": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "filters": {
      "search": null,
      "gradeLevel": null,
      "section": null,
      "status": null
    },
    "sorting": {
      "sortBy": "lastName",
      "sortOrder": "asc"
    }
  }
}
```

### 2. Get Student Details

**GET** `/api/students/{id}`

Retrieve detailed information about a specific student.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Student ID (CUID) |

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `startDate` | date | - | Filter attendance from this date |
| `endDate` | date | - | Filter attendance to this date |
| `includeDetails` | boolean | false | Include detailed attendance records |

#### Response

```json
{
  "success": true,
  "data": {
    "id": "student_id",
    "studentNumber": "2024-001",
    "firstName": "John",
    "lastName": "Doe",
    "middleName": "Smith",
    "fullName": "John Smith Doe",
    "gradeLevel": "G10",
    "section": "A",
    "guardianName": "Jane Doe",
    "guardianContact": "+************",
    "address": "123 Main St, City",
    "profilePhoto": "/uploads/students/photos/photo.jpg",
    "status": "ACTIVE",
    "dateEnrolled": "2024-01-15T00:00:00.000Z",
    "createdAt": "2024-01-15T08:00:00.000Z",
    "updatedAt": "2024-01-15T08:00:00.000Z",
    "attendances": [
      {
        "id": "attendance_id",
        "date": "2024-01-20",
        "timeIn": "08:00:00",
        "timeOut": "17:00:00",
        "status": "PRESENT",
        "teacher": {
          "firstName": "Teacher",
          "lastName": "Name",
          "employeeNumber": "T001"
        }
      }
    ],
    "qrCodes": [
      {
        "id": "qr_id",
        "qrData": "QRSAMS_student_id_timestamp_random",
        "generatedAt": "2024-01-20T08:00:00.000Z",
        "expiresAt": "2024-01-21T08:00:00.000Z",
        "isActive": true
      }
    ],
    "smsLogs": []
  }
}
```

### 3. Create Student

**POST** `/api/students`

Create a new student record. Requires ADMIN or STAFF role.

#### Request Body

```json
{
  "studentNumber": "2024-002",
  "firstName": "Jane",
  "lastName": "Smith",
  "middleName": "Doe",
  "gradeLevel": "G11",
  "section": "B",
  "guardianName": "John Smith",
  "guardianContact": "+************",
  "address": "456 Oak Ave, City",
  "status": "ACTIVE"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "new_student_id",
    "studentNumber": "2024-002",
    "firstName": "Jane",
    "lastName": "Smith",
    "middleName": "Doe",
    "fullName": "Jane Doe Smith",
    "gradeLevel": "G11",
    "section": "B",
    "guardianName": "John Smith",
    "guardianContact": "+************",
    "address": "456 Oak Ave, City",
    "profilePhoto": null,
    "status": "ACTIVE",
    "dateEnrolled": "2024-01-20T08:00:00.000Z",
    "createdAt": "2024-01-20T08:00:00.000Z",
    "updatedAt": "2024-01-20T08:00:00.000Z",
    "attendances": [],
    "qrCodes": [],
    "smsLogs": []
  },
  "message": "Student created successfully"
}
```

### 4. Update Student

**PUT** `/api/students/{id}`

Update an existing student record. Requires ADMIN or STAFF role.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Student ID (CUID) |

#### Request Body

All fields are optional. Only provided fields will be updated.

```json
{
  "studentNumber": "2024-002-UPDATED",
  "firstName": "Jane",
  "lastName": "Smith",
  "middleName": "Marie",
  "gradeLevel": "G12",
  "section": "A",
  "guardianName": "John Smith",
  "guardianContact": "+************",
  "address": "789 Pine St, City",
  "status": "ACTIVE"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "student_id",
    "studentNumber": "2024-002-UPDATED",
    "firstName": "Jane",
    "lastName": "Smith",
    "middleName": "Marie",
    "fullName": "Jane Marie Smith",
    "gradeLevel": "G12",
    "section": "A",
    "guardianName": "John Smith",
    "guardianContact": "+************",
    "address": "789 Pine St, City",
    "profilePhoto": null,
    "status": "ACTIVE",
    "dateEnrolled": "2024-01-20T08:00:00.000Z",
    "createdAt": "2024-01-20T08:00:00.000Z",
    "updatedAt": "2024-01-21T10:30:00.000Z",
    "attendances": [],
    "qrCodes": [],
    "smsLogs": []
  },
  "message": "Student updated successfully"
}
```

### 5. Delete Student

**DELETE** `/api/students/{id}`

Soft delete a student record (sets status to INACTIVE). Requires ADMIN role.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Student ID (CUID) |

#### Response

```json
{
  "success": true,
  "message": "Student deleted successfully"
}
```

### 6. Generate QR Code

**POST** `/api/students/{id}/qr-generate`

Generate a new QR code for a student. Requires ADMIN or STAFF role.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Student ID (CUID) |

#### Request Body

```json
{
  "expiryHours": 24,
  "batchGeneration": false
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "qr_code_id",
    "qrData": "QRSAMS_student_id_1642680000000_abc123def456",
    "generatedAt": "2024-01-20T08:00:00.000Z",
    "expiresAt": "2024-01-21T08:00:00.000Z",
    "isActive": true,
    "student": {
      "id": "student_id",
      "studentNumber": "2024-001",
      "firstName": "John",
      "lastName": "Doe",
      "gradeLevel": "G10",
      "section": "A"
    }
  },
  "message": "QR code generated successfully"
}
```

### 7. Lookup Student by QR Code

**GET** `/api/students/qr/{qrCode}`

Retrieve student information using a QR code.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `qrCode` | string | QR code data |

#### Response

```json
{
  "success": true,
  "data": {
    "id": "student_id",
    "studentNumber": "2024-001",
    "firstName": "John",
    "lastName": "Doe",
    "middleName": "Smith",
    "fullName": "John Smith Doe",
    "gradeLevel": "G10",
    "section": "A",
    "guardianName": "Jane Doe",
    "guardianContact": "+************",
    "address": "123 Main St, City",
    "profilePhoto": "/uploads/students/photos/photo.jpg",
    "status": "ACTIVE",
    "dateEnrolled": "2024-01-15T00:00:00.000Z",
    "createdAt": "2024-01-15T08:00:00.000Z",
    "updatedAt": "2024-01-15T08:00:00.000Z",
    "attendances": [],
    "qrCodes": [],
    "smsLogs": [],
    "qrCodeInfo": {
      "id": "qr_code_id",
      "qrData": "QRSAMS_student_id_1642680000000_abc123def456",
      "generatedAt": "2024-01-20T08:00:00.000Z",
      "expiresAt": "2024-01-21T08:00:00.000Z",
      "isActive": true
    }
  }
}
```

### 8. Upload Student Photo

**POST** `/api/students/{id}/photo`

Upload a profile photo for a student. Requires ADMIN or STAFF role.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Student ID (CUID) |

#### Request Body

Form data with the following field:
- `photo`: Image file (JPEG, PNG, WebP, max 5MB)

#### Response

```json
{
  "success": true,
  "data": {
    "photoUrl": "/uploads/students/photos/photo_1642680000000_abc123.jpg",
    "filename": "photo_1642680000000_abc123.jpg",
    "size": 1024000,
    "type": "image/jpeg"
  },
  "message": "Photo uploaded successfully"
}
```

### 9. Delete Student Photo

**DELETE** `/api/students/{id}/photo`

Delete a student's profile photo. Requires ADMIN or STAFF role.

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Student ID (CUID) |

#### Response

```json
{
  "success": true,
  "message": "Photo deleted successfully"
}
```

### 10. Bulk Import Students

**POST** `/api/students/bulk-import`

Import multiple students from a CSV file. Requires ADMIN role.

#### Request Body

Form data with the following fields:
- `file`: CSV file (max 10MB, max 1000 records)
- `hasHeader`: boolean (default: true)
- `skipErrors`: boolean (default: false)
- `mapping`: JSON string for column mapping (optional)

#### CSV Format

```csv
studentNumber,firstName,lastName,middleName,gradeLevel,section,guardianName,guardianContact,address
2024-001,John,Doe,Smith,G10,A,Jane Doe,+************,123 Main St
2024-002,Jane,Smith,Marie,G11,B,John Smith,+************,456 Oak Ave
```

#### Response

```json
{
  "success": true,
  "data": {
    "imported": 150,
    "failed": 5,
    "total": 155,
    "errors": [
      {
        "row": 10,
        "errors": ["Student number 2024-010 already exists"]
      }
    ]
  },
  "message": "Successfully imported 150 students"
}
```

### 11. Export Students

**GET** `/api/students/export`

Export student data in various formats. Requires ADMIN or STAFF role.

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `format` | enum | csv | Export format (csv, excel, pdf) |
| `fields` | array | all | Fields to include in export |
| `filters` | object | - | Same filters as list students |
| `includePhotos` | boolean | false | Include photo URLs |

#### Response

Returns a file download with appropriate content type and filename.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {}
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `AUTHENTICATION_ERROR` | 401 | Authentication required or invalid token |
| `AUTHORIZATION_ERROR` | 403 | Insufficient permissions |
| `VALIDATION_ERROR` | 400 | Invalid input data |
| `NOT_FOUND_ERROR` | 404 | Resource not found |
| `CONFLICT_ERROR` | 409 | Duplicate data or constraint violation |
| `RATE_LIMIT_ERROR` | 429 | Too many requests |
| `FILE_UPLOAD_ERROR` | 400 | File upload failed |
| `INTERNAL_ERROR` | 500 | Server error |

## Data Validation

### Student Number Format
- Pattern: `^[A-Z0-9\-]+$`
- Length: 1-20 characters
- Must be unique

### Name Fields
- Pattern: `^[a-zA-Z\s\-'\.]+$`
- Length: 1-50 characters for first/last name
- Length: 0-50 characters for middle name (optional)

### Guardian Contact
- Pattern: `^(\+63|0)[0-9]{10}$`
- Philippine phone number format

### Grade Levels
Valid values: K1, K2, G1, G2, G3, G4, G5, G6, G7, G8, G9, G10, G11, G12

### Sections
Valid values: A, B, C, D, E, F, G, H, I, J

### Student Status
- `ACTIVE`: Currently enrolled
- `INACTIVE`: Temporarily inactive
- `GRADUATED`: Completed studies
- `TRANSFERRED`: Moved to another school
- `DROPPED`: Discontinued studies

## File Upload Specifications

### Profile Photos
- Allowed types: JPEG, PNG, WebP
- Maximum size: 5MB
- Recommended dimensions: 300x300px
- Storage path: `/public/uploads/students/photos/`

### CSV Import Files
- File type: text/csv
- Maximum size: 10MB
- Maximum records: 1000 per import
- Encoding: UTF-8

## Rate Limiting Details

Rate limits are applied per IP address and reset every minute:

| Endpoint Category | Limit | Window |
|------------------|-------|---------|
| List/Search | 100 requests | 1 minute |
| Individual CRUD | 50 requests | 1 minute |
| QR Generation | 20 requests | 1 minute |
| Photo Upload | 10 requests | 1 minute |
| Bulk Import | 2 requests | 1 minute |
| Export | 5 requests | 1 minute |

When rate limit is exceeded, the API returns a 429 status with `retryAfter` field indicating when to retry.

## Audit Logging

All operations are automatically logged for audit purposes, including:
- User ID and role
- Action performed
- Resource affected
- Timestamp
- Request details
- Success/failure status

Audit logs are stored securely and can be accessed by administrators for compliance and security monitoring.