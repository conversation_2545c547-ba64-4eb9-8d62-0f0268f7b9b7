import jwt from 'jsonwebtoken'
import { SignJWT, jwtVerify } from 'jose'
import bcrypt from 'bcryptjs'
import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'
import { prisma } from './db'
import { UserRole } from '../generated/prisma'

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET!
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET!
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m'
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d'
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12')

// JWT secrets as Uint8Array for jose
const JWT_SECRET_KEY = new TextEncoder().encode(JWT_SECRET)
const JWT_REFRESH_SECRET_KEY = new TextEncoder().encode(JWT_REFRESH_SECRET)

// JWT Payload Interface
export interface JWTPayload {
  userId: string
  username: string
  email: string
  role: UserRole
  firstName: string
  lastName: string
  iat?: number
  exp?: number
}

// Refresh Token Payload Interface
export interface RefreshTokenPayload {
  userId: string
  tokenVersion: number
  iat?: number
  exp?: number
}

// Authentication Result Interface
export interface AuthResult {
  success: boolean
  user?: {
    id: string
    username: string
    email: string
    firstName: string
    lastName: string
    role: UserRole
    lastLoginAt: Date | null
  }
  accessToken?: string
  refreshToken?: string
  message?: string
  error?: string
}

// Password Utilities
export const passwordUtils = {
  hash: async (password: string): Promise<string> => {
    return bcrypt.hash(password, BCRYPT_ROUNDS)
  },

  verify: async (password: string, hash: string): Promise<boolean> => {
    return bcrypt.compare(password, hash)
  },

  generateResetToken: (): string => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }
}

// JWT Utilities
export const jwtUtils = {
  sign: async (payload: JWTPayload): Promise<string> => {
    const jwt = await new SignJWT(payload as any)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setIssuer('qrsams')
      .setAudience('qrsams-users')
      .setExpirationTime('15m')
      .sign(JWT_SECRET_KEY)

    return jwt
  },

  signRefresh: async (payload: RefreshTokenPayload): Promise<string> => {
    const jwt = await new SignJWT(payload as any)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setIssuer('qrsams')
      .setAudience('qrsams-refresh')
      .setExpirationTime('7d')
      .sign(JWT_REFRESH_SECRET_KEY)

    return jwt
  },

  verify: async (token: string): Promise<JWTPayload | null> => {
    try {
      const { payload } = await jwtVerify(token, JWT_SECRET_KEY, {
        issuer: 'qrsams',
        audience: 'qrsams-users'
      })

      // Validate payload structure
      if (
        typeof payload.userId === 'string' &&
        typeof payload.username === 'string' &&
        typeof payload.email === 'string' &&
        typeof payload.role === 'string' &&
        typeof payload.firstName === 'string' &&
        typeof payload.lastName === 'string'
      ) {
        return payload as unknown as JWTPayload
      }

      return null
    } catch (error) {
      console.error('JWT verification failed:', error)
      return null
    }
  },

  verifyRefresh: async (token: string): Promise<RefreshTokenPayload | null> => {
    try {
      const { payload } = await jwtVerify(token, JWT_REFRESH_SECRET_KEY, {
        issuer: 'qrsams',
        audience: 'qrsams-refresh'
      })

      if (
        typeof payload.userId === 'string' &&
        typeof payload.tokenVersion === 'number'
      ) {
        return payload as unknown as RefreshTokenPayload
      }

      return null
    } catch (error) {
      console.error('Refresh token verification failed:', error)
      return null
    }
  },

  decode: (token: string): JWTPayload | null => {
    try {
      return jwt.decode(token) as JWTPayload
    } catch (error) {
      console.error('JWT decode failed:', error)
      return null
    }
  }
}

// Cookie Utilities
export const cookieUtils = {
  setAuthCookies: (accessToken: string, refreshToken: string) => {
    const cookieStore = cookies()
    
    // Set access token cookie (httpOnly, secure, sameSite)
    cookieStore.set('access-token', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60, // 15 minutes
      path: '/'
    })

    // Set refresh token cookie (httpOnly, secure, sameSite)
    cookieStore.set('refresh-token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    })
  },

  clearAuthCookies: () => {
    const cookieStore = cookies()
    cookieStore.delete('access-token')
    cookieStore.delete('refresh-token')
  },

  getTokenFromRequest: (request: NextRequest): string | null => {
    // Try to get token from Authorization header first
    const authHeader = request.headers.get('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7)
    }

    // Fallback to cookie
    return request.cookies.get('access-token')?.value || null
  },

  getRefreshTokenFromRequest: (request: NextRequest): string | null => {
    return request.cookies.get('refresh-token')?.value || null
  }
}

// Authentication Functions
export const authenticate = async (username: string, password: string): Promise<AuthResult> => {
  try {
    // Find user by username or email
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: username }
        ],
        isActive: true
      }
    })

    if (!user) {
      return {
        success: false,
        error: 'Invalid credentials'
      }
    }

    // Check if account is locked
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      const lockoutMinutes = Math.ceil((user.lockedUntil.getTime() - Date.now()) / (1000 * 60))
      return {
        success: false,
        error: `Account is locked. Try again in ${lockoutMinutes} minutes.`
      }
    }

    // Verify password
    const isValidPassword = await passwordUtils.verify(password, user.passwordHash)
    
    if (!isValidPassword) {
      // Increment login attempts
      const attempts = user.loginAttempts + 1
      const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5')
      const lockoutTime = parseInt(process.env.LOCKOUT_TIME_MINUTES || '15')
      
      const updateData: any = { loginAttempts: attempts }
      
      if (attempts >= maxAttempts) {
        updateData.lockedUntil = new Date(Date.now() + lockoutTime * 60 * 1000)
      }

      await prisma.user.update({
        where: { id: user.id },
        data: updateData
      })

      return {
        success: false,
        error: attempts >= maxAttempts 
          ? `Account locked due to too many failed attempts. Try again in ${lockoutTime} minutes.`
          : 'Invalid credentials'
      }
    }

    // Reset login attempts and update last login
    await prisma.user.update({
      where: { id: user.id },
      data: {
        loginAttempts: 0,
        lockedUntil: null,
        lastLoginAt: new Date()
      }
    })

    // Generate tokens
    const jwtPayload: JWTPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName
    }

    const refreshPayload: RefreshTokenPayload = {
      userId: user.id,
      tokenVersion: 1 // This could be stored in DB for token invalidation
    }

    const accessToken = await jwtUtils.sign(jwtPayload)
    const refreshToken = await jwtUtils.signRefresh(refreshPayload)

    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        lastLoginAt: user.lastLoginAt
      },
      accessToken,
      refreshToken
    }

  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed'
    }
  }
}

// Get user from token
export const getUserFromToken = async (token: string): Promise<AuthResult['user'] | null> => {
  try {
    const payload = await jwtUtils.verify(token)
    if (!payload) return null

    const user = await prisma.user.findUnique({
      where: {
        id: payload.userId,
        isActive: true
      }
    })

    if (!user) return null

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      lastLoginAt: user.lastLoginAt
    }
  } catch (error) {
    console.error('Get user from token error:', error)
    return null
  }
}

// Refresh token
export const refreshAccessToken = async (refreshToken: string): Promise<{ accessToken: string } | null> => {
  try {
    const payload = await jwtUtils.verifyRefresh(refreshToken)
    if (!payload) return null

    const user = await prisma.user.findUnique({
      where: {
        id: payload.userId,
        isActive: true
      }
    })

    if (!user) return null

    const jwtPayload: JWTPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName
    }

    const accessToken = await jwtUtils.sign(jwtPayload)
    return { accessToken }

  } catch (error) {
    console.error('Refresh token error:', error)
    return null
  }
}
