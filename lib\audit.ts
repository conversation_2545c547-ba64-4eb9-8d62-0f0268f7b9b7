import { prisma } from './db'
import { AuditAction } from '../generated/prisma'
import { NextRequest } from 'next/server'

// Audit Log Interface
export interface AuditLogData {
  userId?: string
  action: AuditAction
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// Extract client information from request
export const extractClientInfo = (request: NextRequest) => {
  const ipAddress = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   request.ip || 
                   'unknown'
  
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  return { ipAddress, userAgent }
}

// Create audit log entry
export const createAuditLog = async (data: AuditLogData): Promise<void> => {
  try {
    await prisma.auditLog.create({
      data: {
        userId: data.userId,
        action: data.action,
        resource: data.resource,
        resourceId: data.resourceId,
        details: data.details ? JSON.stringify(data.details) : null,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        timestamp: new Date()
      }
    })
  } catch (error) {
    console.error('Failed to create audit log:', error)
    // Don't throw error to avoid breaking the main operation
  }
}

// Audit log helpers for common actions
export const auditHelpers = {
  // Authentication events
  login: async (userId: string, request: NextRequest, success: boolean) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.LOGIN,
      resource: 'authentication',
      details: { success },
      ipAddress,
      userAgent
    })
  },

  logout: async (userId: string, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.LOGOUT,
      resource: 'authentication',
      ipAddress,
      userAgent
    })
  },

  // User management events
  createUser: async (adminUserId: string, newUserId: string, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId: adminUserId,
      action: AuditAction.CREATE,
      resource: 'user',
      resourceId: newUserId,
      ipAddress,
      userAgent
    })
  },

  updateUser: async (adminUserId: string, targetUserId: string, changes: Record<string, any>, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId: adminUserId,
      action: AuditAction.UPDATE,
      resource: 'user',
      resourceId: targetUserId,
      details: { changes },
      ipAddress,
      userAgent
    })
  },

  deleteUser: async (adminUserId: string, deletedUserId: string, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId: adminUserId,
      action: AuditAction.DELETE,
      resource: 'user',
      resourceId: deletedUserId,
      ipAddress,
      userAgent
    })
  },

  // Student management events
  createStudent: async (userId: string, studentId: string, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.CREATE,
      resource: 'student',
      resourceId: studentId,
      ipAddress,
      userAgent
    })
  },

  updateStudent: async (userId: string, studentId: string, changes: Record<string, any>, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.UPDATE,
      resource: 'student',
      resourceId: studentId,
      details: { changes },
      ipAddress,
      userAgent
    })
  },

  // Attendance events
  markAttendance: async (userId: string, attendanceId: string, studentId: string, status: string, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.CREATE,
      resource: 'attendance',
      resourceId: attendanceId,
      details: { studentId, status },
      ipAddress,
      userAgent
    })
  },

  // Report events
  generateReport: async (userId: string, reportType: string, filters: Record<string, any>, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.EXPORT,
      resource: 'report',
      details: { reportType, filters },
      ipAddress,
      userAgent
    })
  },

  // Data access events
  viewData: async (userId: string, resource: string, resourceId?: string, request?: NextRequest) => {
    const clientInfo = request ? extractClientInfo(request) : { ipAddress: 'unknown', userAgent: 'unknown' }
    await createAuditLog({
      userId,
      action: AuditAction.VIEW,
      resource,
      resourceId,
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent
    })
  },

  // Import/Export events
  importData: async (userId: string, resource: string, count: number, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.IMPORT,
      resource,
      details: { recordCount: count },
      ipAddress,
      userAgent
    })
  },

  exportData: async (userId: string, resource: string, filters: Record<string, any>, request: NextRequest) => {
    const { ipAddress, userAgent } = extractClientInfo(request)
    await createAuditLog({
      userId,
      action: AuditAction.EXPORT,
      resource,
      details: { filters },
      ipAddress,
      userAgent
    })
  }
}

// Get audit logs with pagination and filtering
export interface AuditLogFilters {
  userId?: string
  action?: AuditAction
  resource?: string
  startDate?: Date
  endDate?: Date
  page?: number
  limit?: number
}

export const getAuditLogs = async (filters: AuditLogFilters = {}) => {
  const {
    userId,
    action,
    resource,
    startDate,
    endDate,
    page = 1,
    limit = 50
  } = filters

  const where: any = {}

  if (userId) where.userId = userId
  if (action) where.action = action
  if (resource) where.resource = resource
  if (startDate || endDate) {
    where.timestamp = {}
    if (startDate) where.timestamp.gte = startDate
    if (endDate) where.timestamp.lte = endDate
  }

  const [logs, total] = await Promise.all([
    prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            username: true,
            firstName: true,
            lastName: true,
            role: true
          }
        }
      },
      orderBy: { timestamp: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    }),
    prisma.auditLog.count({ where })
  ])

  return {
    logs: logs.map(log => ({
      ...log,
      details: log.details ? JSON.parse(log.details) : null
    })),
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }
}

// Get audit statistics
export const getAuditStats = async (days: number = 30) => {
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)

  const stats = await prisma.auditLog.groupBy({
    by: ['action'],
    where: {
      timestamp: {
        gte: startDate
      }
    },
    _count: {
      action: true
    }
  })

  const totalLogs = await prisma.auditLog.count({
    where: {
      timestamp: {
        gte: startDate
      }
    }
  })

  return {
    totalLogs,
    actionStats: stats.map(stat => ({
      action: stat.action,
      count: stat._count.action
    })),
    period: `${days} days`
  }
}
