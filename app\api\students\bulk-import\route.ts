import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import { csvImportSchema } from '@/lib/validations/student'
import { csvUtils, studentDbUtils } from '@/lib/utils/student'

/**
 * POST /api/students/bulk-import
 * Import students from CSV file
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'bulk-import', 2, 60 * 1000) // 2 imports per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN can bulk import)
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions. Only administrators can bulk import students.' },
        { status: 403 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const hasHeader = formData.get('hasHeader') === 'true'
    const skipErrors = formData.get('skipErrors') === 'true'
    const mapping = formData.get('mapping') ? JSON.parse(formData.get('mapping') as string) : undefined

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No CSV file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only CSV files are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      )
    }

    try {
      // Read and parse CSV content
      const csvContent = await file.text()
      const { headers, rows } = csvUtils.parseCSV(csvContent, hasHeader)

      if (rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'CSV file is empty or contains no valid data' },
          { status: 400 }
        )
      }

      if (rows.length > 1000) {
        return NextResponse.json(
          { success: false, error: 'Too many records. Maximum 1000 students per import.' },
          { status: 400 }
        )
      }

      // Validate and process each row
      const validStudents: any[] = []
      const errors: Array<{ row: number, errors: string[] }> = []

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i] as Record<string, string>
        const validation = csvUtils.validateCSVRow(row, mapping)

        if (validation.isValid) {
          // Check if student number already exists
          const exists = await studentDbUtils.checkStudentNumberExists(validation.data.studentNumber)
          if (exists) {
            if (!skipErrors) {
              errors.push({
                row: i + 1,
                errors: [`Student number ${validation.data.studentNumber} already exists`]
              })
              continue
            }
          } else {
            validStudents.push({
              studentNumber: validation.data.studentNumber,
              firstName: validation.data.firstName,
              lastName: validation.data.lastName,
              middleName: validation.data.middleName || null,
              gradeLevel: validation.data.gradeLevel,
              section: validation.data.section,
              guardianName: validation.data.guardianName,
              guardianContact: validation.data.guardianContact,
              address: validation.data.address,
              status: 'ACTIVE',
              dateEnrolled: new Date()
            })
          }
        } else {
          if (!skipErrors) {
            errors.push({ row: i + 1, errors: validation.errors })
          }
        }
      }

      // If there are errors and skipErrors is false, return errors
      if (errors.length > 0 && !skipErrors) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation errors found in CSV data',
            errors: errors.slice(0, 10), // Limit to first 10 errors
            totalErrors: errors.length
          },
          { status: 400 }
        )
      }

      // Import valid students in transaction
      const importResults = await prisma.$transaction(async (tx) => {
        const createdStudents = []

        for (const studentData of validStudents) {
          const student = await tx.student.create({
            data: studentData
          })

          // Generate QR code for each student
          const qrData = `QRSAMS_${student.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          await tx.qRCode.create({
            data: {
              studentId: student.id,
              qrData,
              isActive: true,
              expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
            }
          })

          createdStudents.push(student)
        }

        return createdStudents
      })

      // Log audit trail
      await auditHelpers.log({
        userId: user.id,
        action: 'IMPORT',
        resource: 'students',
        details: {
          totalRows: rows.length,
          validStudents: validStudents.length,
          importedStudents: importResults.length,
          errors: errors.length,
          filename: file.name,
          fileSize: file.size
        }
      }, request)

      return NextResponse.json({
        success: true,
        data: {
          imported: importResults.length,
          failed: errors.length,
          total: rows.length,
          errors: errors.slice(0, 5) // Return first 5 errors for reference
        },
        message: `Successfully imported ${importResults.length} students`
      }, { status: 201 })

    } catch (parseError) {
      console.error('CSV parsing error:', parseError)
      return NextResponse.json(
        { success: false, error: 'Failed to parse CSV file. Please check the file format.' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('POST /api/students/bulk-import error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}