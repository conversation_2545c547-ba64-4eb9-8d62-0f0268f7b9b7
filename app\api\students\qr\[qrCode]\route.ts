import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import { qrLookupSchema } from '@/lib/validations/student'
import { qrCodeUtils, transformUtils } from '@/lib/utils/student'

/**
 * GET /api/students/qr/[qrCode]
 * Get student by QR code
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { qrCode: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'qr-lookup', 100, 60 * 1000) // 100 lookups per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Validate QR code parameter
    const paramValidation = qrLookupSchema.safeParse({ qrCode: params.qrCode })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid QR code format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Validate QR code format
    if (!qrCodeUtils.validateQRFormat(params.qrCode)) {
      return NextResponse.json(
        { success: false, error: 'Invalid QR code format' },
        { status: 400 }
      )
    }

    // Find QR code in database
    const qrCode = await prisma.qRCode.findUnique({
      where: { qrData: params.qrCode },
      include: {
        student: {
          include: {
            attendances: {
              orderBy: { date: 'desc' },
              take: 5,
              include: {
                teacher: {
                  select: {
                    firstName: true,
                    lastName: true,
                    employeeNumber: true
                  }
                }
              }
            },
            qrCodes: {
              where: { isActive: true },
              orderBy: { generatedAt: 'desc' },
              take: 1
            }
          }
        }
      }
    })

    if (!qrCode) {
      return NextResponse.json(
        { success: false, error: 'QR code not found' },
        { status: 404 }
      )
    }

    // Check if QR code is active
    if (!qrCode.isActive) {
      return NextResponse.json(
        { success: false, error: 'QR code is inactive' },
        { status: 400 }
      )
    }

    // Check if QR code has expired
    if (qrCode.expiresAt && qrCode.expiresAt < new Date()) {
      // Deactivate expired QR code
      await prisma.qRCode.update({
        where: { id: qrCode.id },
        data: { isActive: false }
      })

      return NextResponse.json(
        { success: false, error: 'QR code has expired' },
        { status: 400 }
      )
    }

    // Check if student is active
    if (qrCode.student.status !== 'ACTIVE') {
      return NextResponse.json(
        { success: false, error: 'Student is not active' },
        { status: 400 }
      )
    }

    // Transform student data for response
    const transformedStudent = transformUtils.transformStudentForResponse(qrCode.student)

    // Add QR code information
    const responseData = {
      ...transformedStudent,
      qrCodeInfo: {
        id: qrCode.id,
        qrData: qrCode.qrData,
        generatedAt: qrCode.generatedAt,
        expiresAt: qrCode.expiresAt,
        isActive: qrCode.isActive
      }
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'qr_lookup',
      resourceId: qrCode.id,
      details: {
        qrData: qrCode.qrData,
        studentId: qrCode.student.id,
        studentNumber: qrCode.student.studentNumber,
        studentName: `${qrCode.student.firstName} ${qrCode.student.lastName}`
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: responseData
    })

  } catch (error) {
    console.error('GET /api/students/qr/[qrCode] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}