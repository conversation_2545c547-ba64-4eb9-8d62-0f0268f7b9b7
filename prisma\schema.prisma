// This is your Prisma schema file for QR-Code Based Student Attendance and Monitoring System
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enums for better type safety
enum StudentStatus {
  ACTIVE
  INACTIVE
  GRADUATED
  TRANSFERRED
  DROPPED
}

enum TeacherStatus {
  ACTIVE
  INACTIVE
  ON_LEAVE
  TERMINATED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum SMSStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
}

enum UserRole {
  ADMIN
  TEACHER
  STAFF
}

enum AuditAction {
  LOGIN
  LOGOUT
  CREATE
  UPDATE
  DELETE
  VIEW
  EXPORT
  IMPORT
}

// Users table for authentication
model User {
  id              String    @id @default(cuid())
  username        String    @unique
  email           String    @unique
  passwordHash    String
  firstName       String
  lastName        String
  middleName      String?
  role            UserRole  @default(STAFF)
  isActive        Boolean   @default(true)
  lastLoginAt     DateTime?
  loginAttempts   Int       @default(0)
  lockedUntil     DateTime?
  passwordResetToken String?
  passwordResetExpires DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  auditLogs       AuditLog[]
  teacherProfile  Teacher?  @relation(fields: [teacherProfileId], references: [id])
  teacherProfileId String?

  @@map("users")
  @@index([username])
  @@index([email])
  @@index([role])
  @@index([isActive])
}

// Audit Log table for tracking user actions
model AuditLog {
  id          String      @id @default(cuid())
  userId      String?
  action      AuditAction
  resource    String      // e.g., "student", "attendance", "user"
  resourceId  String?     // ID of the affected resource
  details     String?     // JSON string with additional details
  ipAddress   String?
  userAgent   String?
  timestamp   DateTime    @default(now())

  // Relations
  user        User?       @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
}

// Students table
model Student {
  id             String        @id @default(cuid())
  studentNumber  String        @unique
  firstName      String
  lastName       String
  middleName     String?
  gradeLevel     String
  section        String
  guardianName   String
  guardianContact String
  address        String
  profilePhoto   String?
  dateEnrolled   DateTime      @default(now())
  status         StudentStatus @default(ACTIVE)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // Relations
  attendances    Attendance[]
  qrCodes        QRCode[]
  smsLogs        SMSLog[]

  @@map("students")
  @@index([studentNumber])
  @@index([gradeLevel, section])
  @@index([status])
}

// Teachers table
model Teacher {
  id              String        @id @default(cuid())
  employeeNumber  String        @unique
  firstName       String
  lastName        String
  middleName      String?
  contactNumber   String
  email           String        @unique
  subjectsHandled String        // JSON string of subjects array
  advisorySection String?
  status          TeacherStatus @default(ACTIVE)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  attendances     Attendance[]
  classes         Class[]
  user            User[]        // One teacher can have multiple user accounts (if needed)

  @@map("teachers")
  @@index([employeeNumber])
  @@index([email])
  @@index([status])
}

// Attendance table
model Attendance {
  id        String           @id @default(cuid())
  studentId String
  teacherId String
  date      DateTime
  timeIn    DateTime?
  timeOut   DateTime?
  status    AttendanceStatus
  remarks   String?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  student   Student          @relation(fields: [studentId], references: [id], onDelete: Cascade)
  teacher   Teacher          @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  smsLogs   SMSLog[]

  @@map("attendances")
  @@index([studentId])
  @@index([teacherId])
  @@index([date])
  @@index([status])
  @@unique([studentId, date]) // One attendance record per student per day
}

// Classes table
model Class {
  id           String   @id @default(cuid())
  subjectName  String
  teacherId    String
  gradeLevel   String
  section      String
  scheduleTime String   // JSON string for schedule details
  roomNumber   String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  teacher      Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@map("classes")
  @@index([teacherId])
  @@index([gradeLevel, section])
  @@index([subjectName])
}

// SMS Logs table
model SMSLog {
  id              String    @id @default(cuid())
  recipientNumber String
  message         String
  status          SMSStatus @default(PENDING)
  sentAt          DateTime?
  deliveredAt     DateTime?
  studentId       String?
  attendanceId    String?
  createdAt       DateTime  @default(now())

  // Relations
  student         Student?     @relation(fields: [studentId], references: [id], onDelete: SetNull)
  attendance      Attendance?  @relation(fields: [attendanceId], references: [id], onDelete: SetNull)

  @@map("sms_logs")
  @@index([recipientNumber])
  @@index([status])
  @@index([studentId])
  @@index([attendanceId])
  @@index([sentAt])
}

// QR Codes table
model QRCode {
  id          String    @id @default(cuid())
  studentId   String
  qrData      String    @unique
  generatedAt DateTime  @default(now())
  expiresAt   DateTime?
  isActive    Boolean   @default(true)

  // Relations
  student     Student   @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("qr_codes")
  @@index([studentId])
  @@index([qrData])
  @@index([isActive])
  @@index([expiresAt])
}

// System Settings table
model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  updatedAt   DateTime @updatedAt

  @@map("system_settings")
  @@index([key])
}
