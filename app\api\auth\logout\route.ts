import { NextRequest, NextResponse } from 'next/server'
import { cookieUtils, getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'

export async function POST(request: NextRequest) {
  try {
    // Get current user from token for audit logging
    const token = cookieUtils.getTokenFromRequest(request)
    let userId: string | undefined

    if (token) {
      const user = await getUserFromToken(token)
      userId = user?.id
    }

    // Log logout event
    if (userId) {
      await auditHelpers.logout(userId, request)
    }

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    })

    // Clear authentication cookies
    response.cookies.set('access-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    })

    response.cookies.set('refresh-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    })

    return response

  } catch (error) {
    console.error('Logout API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
