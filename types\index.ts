// User and Authentication Types
export interface User {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  isActive: boolean
  lastLoginAt: Date | null
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'ADMIN' | 'TEACHER' | 'STAFF'

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

// JWT Token Types
export interface JWTPayload {
  userId: string
  username: string
  email: string
  role: UserRole
  firstName: string
  lastName: string
  iat?: number
  exp?: number
}

export interface RefreshTokenPayload {
  userId: string
  tokenVersion: number
  iat?: number
  exp?: number
}

// Login Form Types
export interface LoginFormData {
  username: string
  password: string
  rememberMe: boolean
}

export interface LoginResponse {
  success: boolean
  user?: User
  accessToken?: string
  refreshToken?: string
  message?: string
  error?: string
}

// Password Reset Types
export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  password: string
  confirmPassword: string
}

export interface PasswordValidation {
  isValid: boolean
  errors: string[]
  score: number
}

// Authentication Result Types
export interface AuthResult {
  success: boolean
  user?: User
  accessToken?: string
  refreshToken?: string
  message?: string
  error?: string
}

// Security Types
export interface SecurityConfig {
  maxLoginAttempts: number
  lockoutTimeMinutes: number
  sessionTimeoutMinutes: number
  passwordMinLength: number
  passwordRequireUppercase: boolean
  passwordRequireLowercase: boolean
  passwordRequireNumbers: boolean
  passwordRequireSpecialChars: boolean
}

export interface RateLimitResult {
  success: boolean
  limit: number
  remaining: number
  resetTime: number
  retryAfter?: number
}

export interface RequestValidation {
  isValid: boolean
  errors: string[]
  riskLevel: 'low' | 'medium' | 'high'
}

// Audit Log Types
export interface AuditLogData {
  userId?: string
  action: AuditAction
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export type AuditAction =
  | 'LOGIN'
  | 'LOGOUT'
  | 'CREATE'
  | 'UPDATE'
  | 'DELETE'
  | 'VIEW'
  | 'EXPORT'
  | 'IMPORT'
  | 'PASSWORD_RESET_REQUEST'
  | 'PASSWORD_RESET_COMPLETE'

export interface AuditLogEntry {
  id: string
  userId?: string
  action: AuditAction
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: Date
  user?: {
    username: string
    firstName: string
    lastName: string
    role: UserRole
  }
}

export interface AuditLogFilters {
  userId?: string
  action?: AuditAction
  resource?: string
  startDate?: Date
  endDate?: Date
  page?: number
  limit?: number
}

// Session Management Types
export interface SessionInfo {
  id: string
  userId: string
  ipAddress: string
  userAgent: string
  createdAt: Date
  lastActivity: Date
  expiresAt: Date
  isActive: boolean
}

export interface SessionSettings {
  maxDuration: number
  warningTime: number
  allowMultipleSessions: boolean
  trackLocation: boolean
}

// Student Types
export interface Student {
  id: string
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  phone?: string
  dateOfBirth: Date
  address: string
  course: string
  yearLevel: number
  section: string
  status: StudentStatus
  qrCode: string
  avatar?: string
  guardianName?: string
  guardianPhone?: string
  enrollmentDate: Date
  createdAt: Date
  updatedAt: Date
}

export type StudentStatus = 'active' | 'inactive' | 'graduated' | 'dropped'

export interface StudentFormData {
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  phone?: string
  dateOfBirth: string
  address: string
  course: string
  yearLevel: number
  section: string
  guardianName?: string
  guardianPhone?: string
}

// Attendance Types
export interface Attendance {
  id: string
  studentId: string
  student?: Student
  date: Date
  timeIn?: Date
  timeOut?: Date
  status: AttendanceStatus
  location?: string
  notes?: string
  scannedBy: string
  scannedByUser?: User
  createdAt: Date
  updatedAt: Date
}

export type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused'

export interface AttendanceRecord {
  date: string
  status: AttendanceStatus
  timeIn?: string
  timeOut?: string
  notes?: string
}

export interface AttendanceSummary {
  totalDays: number
  presentDays: number
  absentDays: number
  lateDays: number
  excusedDays: number
  attendanceRate: number
}

// Course and Academic Types
export interface Course {
  id: string
  code: string
  name: string
  description?: string
  department: string
  credits: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Section {
  id: string
  name: string
  course: string
  yearLevel: number
  capacity: number
  currentEnrollment: number
  adviser?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Analytics and Reports Types
export interface DashboardStats {
  totalStudents: number
  presentToday: number
  absentToday: number
  lateToday: number
  attendanceRate: number
  totalCourses: number
  activeSections: number
}

// Enhanced Dashboard Types
export interface WeatherData {
  temperature: number
  condition: string
  humidity: number
  icon: string
}

export interface RecentActivity {
  id: string
  studentName: string
  studentId: string
  action: 'check-in' | 'check-out' | 'late-arrival'
  time: string
  course: string
  location?: string
  avatar?: string
}

export interface AtRiskStudent {
  id: string
  name: string
  studentId: string
  course: string
  attendanceRate: number
  consecutiveAbsences: number
  lastAttendance: string
  riskLevel: 'high' | 'medium' | 'low'
}

export interface UpcomingClass {
  id: string
  subject: string
  instructor: string
  time: string
  room: string
  course: string
  expectedStudents: number
}

export interface SystemNotification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: string
  isRead: boolean
  actionRequired?: boolean
}

export interface AttendanceTrendData {
  date: string
  present: number
  absent: number
  late: number
  attendanceRate: number
}

export interface AttendanceAnalytics {
  dailyAttendance: DailyAttendanceData[]
  weeklyTrends: WeeklyTrendData[]
  monthlyStats: MonthlyStatsData[]
  courseWiseAttendance: CourseAttendanceData[]
}

export interface DailyAttendanceData {
  date: string
  present: number
  absent: number
  late: number
  total: number
}

export interface WeeklyTrendData {
  week: string
  attendanceRate: number
  totalStudents: number
}

export interface MonthlyStatsData {
  month: string
  year: number
  averageAttendance: number
  totalClasses: number
}

export interface CourseAttendanceData {
  course: string
  attendanceRate: number
  totalStudents: number
  presentStudents: number
}

// Enhanced Analytics Types for Dashboard
export type TimePeriod = 'daily' | 'weekly' | 'monthly' | 'custom'

export interface TimePeriodFilter {
  period: TimePeriod
  startDate?: Date
  endDate?: Date
  customRange?: {
    from: Date | undefined
    to: Date | undefined
  }
}

export interface AnalyticsKPI {
  id: string
  title: string
  value: number | string
  previousValue?: number | string
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  format?: 'percentage' | 'number' | 'currency' | 'time'
  icon?: string
  color?: 'green' | 'red' | 'blue' | 'yellow' | 'purple'
}

export interface AttendanceTrendPoint {
  date: string
  present: number
  absent: number
  late: number
  excused: number
  total: number
  attendanceRate: number
  dayOfWeek?: string
  month?: string
  week?: number
}

export interface GradeAttendanceData {
  grade: string
  section: string
  totalStudents: number
  presentStudents: number
  absentStudents: number
  lateStudents: number
  attendanceRate: number
  trend?: number
}

export interface AttendanceStatusDistribution {
  status: AttendanceStatus
  count: number
  percentage: number
  color: string
}

export interface HeatmapData {
  day: string
  hour: number
  value: number
  date: string
}

export interface PeakAttendanceHour {
  hour: string
  count: number
  percentage: number
}

export interface AtRiskStudentDetailed extends AtRiskStudent {
  trends: AttendanceTrendPoint[]
  patterns: AttendancePattern[]
  recommendations: string[]
  interventions: Intervention[]
}

export interface AttendancePattern {
  type: 'chronic_absence' | 'late_pattern' | 'friday_absence' | 'monday_absence' | 'irregular'
  description: string
  frequency: number
  severity: 'low' | 'medium' | 'high'
  detected: Date
}

export interface Intervention {
  id: string
  type: 'counseling' | 'parent_contact' | 'academic_support' | 'health_check'
  description: string
  status: 'pending' | 'in_progress' | 'completed'
  assignedTo?: string
  dueDate?: Date
  createdAt: Date
}

export interface AIInsight {
  id: string
  type: 'prediction' | 'pattern' | 'recommendation' | 'alert'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  category: 'attendance' | 'performance' | 'behavior' | 'risk'
  actionable: boolean
  actions?: string[]
  createdAt: Date
}

export interface PredictiveAnalytics {
  nextWeekPrediction: {
    expectedAttendanceRate: number
    confidence: number
    factors: string[]
  }
  monthlyForecast: AttendanceTrendPoint[]
  riskPredictions: {
    studentsAtRisk: number
    newRiskStudents: AtRiskStudent[]
    improvingStudents: Student[]
  }
}

export interface TopPerformer {
  id: string
  name: string
  studentId: string
  course: string
  section: string
  attendanceRate: number
  streak: number
  avatar?: string
  achievements: string[]
}

export interface AnalyticsFilter {
  courses: string[]
  sections: string[]
  yearLevels: number[]
  attendanceStatus: AttendanceStatus[]
  riskLevels: ('high' | 'medium' | 'low')[]
  dateRange: TimePeriodFilter
}

export interface ChartExportData {
  chartType: string
  title: string
  data: any[]
  metadata: {
    generatedAt: Date
    filters: AnalyticsFilter
    period: TimePeriodFilter
  }
}

export interface DrillDownData {
  level: 'overview' | 'course' | 'section' | 'student'
  context: {
    courseId?: string
    sectionId?: string
    studentId?: string
  }
  data: any
  breadcrumb: string[]
}

// QR Code and Scanning Types
export interface QRScanResult {
  studentId: string
  timestamp: Date
  location?: string
  isValid: boolean
  error?: string
}

export interface ScanSession {
  id: string
  startTime: Date
  endTime?: Date
  location: string
  scannedBy: string
  totalScans: number
  successfulScans: number
  failedScans: number
  isActive: boolean
}

// Enhanced Scanning Interface Types
export interface ScannedStudent {
  id: string
  student: Student
  attendance: Attendance
  timestamp: Date
  scanMethod: 'qr' | 'manual'
}

export interface ScannerSettings {
  soundEnabled: boolean
  batchMode: boolean
  autoMarkPresent: boolean
  offlineMode: boolean
  cameraPermission: 'granted' | 'denied' | 'prompt'
}

export interface OfflineQueueItem {
  id: string
  studentId: string
  action: AttendanceStatus
  timestamp: Date
  location?: string
  notes?: string
}

export interface ScannerState {
  isScanning: boolean
  isProcessing: boolean
  currentStudent: Student | null
  recentScans: ScannedStudent[]
  offlineQueue: OfflineQueueItem[]
  settings: ScannerSettings
  error: string | null
}

// Settings and Configuration Types
export interface AppSettings {
  schoolName: string
  schoolAddress: string
  schoolLogo?: string
  academicYear: string
  semester: string
  attendanceGracePeriod: number // minutes
  autoLogoutTime: number // minutes
  enableNotifications: boolean
  defaultTheme: 'light' | 'dark' | 'system'
  timeZone: string
}

export interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  attendanceAlerts: boolean
  weeklyReports: boolean
  monthlyReports: boolean
}

// Comprehensive Settings Interfaces
export interface GeneralSettings {
  schoolName: string
  schoolAddress: string
  schoolPhone: string
  schoolEmail: string
  schoolWebsite: string
  schoolLogo?: string
  academicYear: string
  semester: 'first' | 'second' | 'summer'
  startDate: Date
  endDate: Date
  timeZone: string
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD'
  language: 'en' | 'fil'
}

export interface ClassSchedule {
  id: string
  className: string
  startTime: string
  endTime: string
  days: string[]
  room: string
  teacher: string
}

export interface AttendanceRules {
  gracePeriodMinutes: number
  autoLogoutMinutes: number
  requireCheckout: boolean
  allowManualEntry: boolean
  weekendTracking: boolean
  lateThresholdMinutes: number
  absentThresholdMinutes: number
  excusedAbsenceRequiresApproval: boolean
}

export interface SMSSettings {
  apiKey: string
  apiSecret: string
  senderName: string
  enabled: boolean
  dailyLimit: number
  monthlyLimit: number
  parentNotifications: boolean
  teacherNotifications: boolean
  adminNotifications: boolean
}

export interface MessageTemplate {
  id: string
  name: string
  type: 'attendance' | 'absence' | 'late' | 'general'
  subject: string
  content: string
  variables: string[]
  isActive: boolean
}

export interface QRCodeSettings {
  expirationMinutes: number
  securityLevel: 'low' | 'medium' | 'high'
  batchSize: number
  printLayout: 'grid' | 'list' | 'cards'
  includeStudentPhoto: boolean
  includeSchoolLogo: boolean
  regenerateOnScan: boolean
  maxScansPerCode: number
}

export interface UserRole {
  id: string
  name: string
  permissions: string[]
  description: string
}

export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  maxAge: number // days
  preventReuse: number // number of previous passwords
}

export interface SessionSettings {
  maxDuration: number // minutes
  warningTime: number // minutes before logout
  allowMultipleSessions: boolean
  trackLocation: boolean
}

export interface BackupSettings {
  autoBackupEnabled: boolean
  backupFrequency: 'daily' | 'weekly' | 'monthly'
  backupTime: string
  retentionDays: number
  includeAttachments: boolean
  cloudBackupEnabled: boolean
  cloudProvider?: 'aws' | 'google' | 'azure'
}

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system'
  primaryColor: string
  accentColor: string
  fontSize: 'small' | 'medium' | 'large'
  compactMode: boolean
  showAnimations: boolean
  customLogo?: string
  customFavicon?: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form and UI Types
export interface FormState {
  isLoading: boolean
  errors: Record<string, string>
  isDirty: boolean
}

export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
}

export interface FilterOptions {
  course?: string
  section?: string
  yearLevel?: number
  status?: StudentStatus | AttendanceStatus
  dateRange?: {
    start: Date
    end: Date
  }
}

// Navigation and Layout Types
export interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  children?: NavItem[]
}

export interface BreadcrumbItem {
  title: string
  href?: string
}

// Error and Loading Types
export interface ErrorState {
  message: string
  code?: string
  details?: any
}

export interface LoadingState {
  isLoading: boolean
  message?: string
}

// Student Management Types
export interface StudentTableColumn {
  key: keyof Student | 'photo' | 'actions'
  label: string
  sortable?: boolean
  width?: string
  render?: (value: any, student: Student) => React.ReactNode
}

export interface StudentFilters {
  search?: string
  grade?: string
  section?: string
  status?: StudentStatus
  course?: string
  yearLevel?: number
  enrollmentDateRange?: {
    start: Date
    end: Date
  }
}

export interface StudentSortConfig {
  field: keyof Student
  direction: 'asc' | 'desc'
}

export interface StudentPagination {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

export interface BulkAction {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: (studentIds: string[]) => Promise<void>
  requiresConfirmation?: boolean
  confirmationMessage?: string
}

export interface StudentStats {
  total: number
  active: number
  inactive: number
  newEnrollments: number
  graduated: number
  dropped: number
}

export interface StudentImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}

export interface StudentExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  fields: (keyof Student)[]
  filters?: StudentFilters
  includePhotos?: boolean
}

export interface GuardianInfo {
  name: string
  relationship: string
  phone: string
  email?: string
  address?: string
  occupation?: string
  emergencyContact: boolean
}

export interface AcademicRecord {
  semester: string
  academicYear: string
  gpa: number
  subjects: Array<{
    code: string
    name: string
    grade: string
    credits: number
  }>
  status: 'enrolled' | 'completed' | 'failed' | 'withdrawn'
}

export interface SMSNotification {
  id: string
  studentId: string
  recipientPhone: string
  message: string
  type: 'attendance' | 'grade' | 'announcement' | 'reminder'
  status: 'pending' | 'sent' | 'failed' | 'delivered'
  sentAt?: Date
  deliveredAt?: Date
  error?: string
}

export interface QRCodeData {
  studentId: string
  generatedAt: Date
  expiresAt?: Date
  isActive: boolean
  scanCount: number
  lastScanned?: Date
}

// Enhanced Student Interface
export interface EnhancedStudent extends Student {
  fullName: string
  guardians: GuardianInfo[]
  academicRecords: AcademicRecord[]
  attendanceSummary: AttendanceSummary
  smsNotifications: SMSNotification[]
  qrCodeData: QRCodeData
  profilePhoto?: string
  emergencyContacts: Array<{
    name: string
    phone: string
    relationship: string
  }>
}

// Philippine Education Reports Types
export type PhilippineReportType = 'SF2' | 'SF4' | 'custom_attendance' | 'sms_notifications' | 'deped_compliance'

export interface PhilippineReportTemplate {
  id: string
  type: PhilippineReportType
  name: string
  description: string
  depedCode?: string
  isOfficial: boolean
  requiredFields: string[]
  optionalFields: string[]
  complianceLevel: 'full' | 'partial' | 'custom'
  digitalSignatureRequired: boolean
  template: ReportTemplate
  createdAt: Date
  updatedAt: Date
}

export interface SF2ReportData {
  schoolYear: string
  month: string
  grade: string
  section: string
  adviser: string
  principalName: string
  schoolName: string
  schoolId: string
  division: string
  region: string
  dailyAttendance: Array<{
    date: Date
    dayOfWeek: string
    malePresent: number
    femalePresent: number
    maleAbsent: number
    femaleAbsent: number
    totalEnrolled: number
    remarks?: string
  }>
  monthlyTotals: {
    totalMaleEnrolled: number
    totalFemaleEnrolled: number
    totalEnrolled: number
    averageDailyAttendance: number
    attendanceRate: number
  }
}

export interface SF4ReportData {
  schoolYear: string
  grade: string
  section: string
  adviser: string
  schoolName: string
  schoolId: string
  division: string
  region: string
  students: Array<{
    lrn: string
    lastName: string
    firstName: string
    middleName?: string
    sex: 'M' | 'F'
    birthDate: Date
    age: number
    motherTongue: string
    religion: string
    address: string
    fatherName?: string
    motherName?: string
    guardianName?: string
    guardianRelationship?: string
    contactNumber?: string
    remarks?: string
  }>
  classStatistics: {
    totalMale: number
    totalFemale: number
    totalStudents: number
    ageDistribution: Record<number, number>
  }
}

export interface ReportParameters {
  reportType: PhilippineReportType
  templateId?: string
  dateRange: {
    start: Date
    end: Date
  }
  filters: {
    grades?: string[]
    sections?: string[]
    courses?: string[]
    teachers?: string[]
    students?: string[]
  }
  format: ReportFormat
  includeCharts: boolean
  includeStatistics: boolean
  includeSignatures: boolean
  customFields?: Record<string, any>
  schedulingOptions?: ReportScheduling
  deliveryOptions?: ReportDelivery
}

export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'print' | 'word'

export type ReportStatus = 'pending' | 'generating' | 'completed' | 'failed' | 'scheduled' | 'delivered'

export interface GeneratedReport {
  id: string
  name: string
  type: PhilippineReportType
  templateId?: string
  parameters: ReportParameters
  status: ReportStatus
  progress?: number
  fileUrl?: string
  fileSize?: number
  generatedBy: string
  generatedAt: Date
  downloadCount: number
  lastDownloaded?: Date
  expiresAt?: Date
  isArchived: boolean
  digitalSignature?: DigitalSignature
  complianceChecks: ComplianceCheck[]
  deliveryStatus?: DeliveryStatus
  error?: string
}

export interface ReportTemplate {
  id: string
  name: string
  description: string
  type: PhilippineReportType
  layout: ReportLayout
  fields: ReportField[]
  styling: ReportStyling
  isDefault: boolean
  isCustom: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export interface ReportLayout {
  orientation: 'portrait' | 'landscape'
  pageSize: 'A4' | 'Letter' | 'Legal'
  margins: {
    top: number
    right: number
    bottom: number
    left: number
  }
  header: ReportSection
  body: ReportSection
  footer: ReportSection
}

export interface ReportSection {
  enabled: boolean
  height?: number
  content: ReportElement[]
}

export interface ReportElement {
  id: string
  type: 'text' | 'table' | 'chart' | 'image' | 'signature' | 'date' | 'page_number'
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  properties: Record<string, any>
  dataBinding?: string
}

export interface ReportField {
  id: string
  name: string
  label: string
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect'
  required: boolean
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    options?: string[]
  }
  depedCompliant: boolean
}

export interface ReportStyling {
  fontFamily: string
  fontSize: number
  colors: {
    primary: string
    secondary: string
    text: string
    background: string
  }
  borders: boolean
  alternatingRows: boolean
  logoUrl?: string
  watermark?: string
}

export interface ReportScheduling {
  enabled: boolean
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  dayOfWeek?: number
  dayOfMonth?: number
  time: string
  timezone: string
  endDate?: Date
  recipients: string[]
  autoArchive: boolean
}

export interface ReportDelivery {
  email: {
    enabled: boolean
    recipients: string[]
    subject: string
    message: string
    attachFormat: ReportFormat[]
  }
  sms: {
    enabled: boolean
    recipients: string[]
    message: string
  }
  print: {
    enabled: boolean
    printerName?: string
    copies: number
    collate: boolean
  }
  cloud: {
    enabled: boolean
    provider: 'google_drive' | 'onedrive' | 'dropbox'
    folder: string
  }
}

export interface DigitalSignature {
  id: string
  signerName: string
  signerTitle: string
  signedAt: Date
  certificateId: string
  isValid: boolean
  signatureImage?: string
  verificationCode: string
}

export interface ComplianceCheck {
  id: string
  rule: string
  description: string
  status: 'passed' | 'failed' | 'warning'
  message: string
  checkedAt: Date
}

export interface DeliveryStatus {
  email: {
    sent: boolean
    sentAt?: Date
    recipients: Array<{
      email: string
      status: 'sent' | 'delivered' | 'failed'
      error?: string
    }>
  }
  sms: {
    sent: boolean
    sentAt?: Date
    recipients: Array<{
      phone: string
      status: 'sent' | 'delivered' | 'failed'
      error?: string
    }>
  }
  print: {
    printed: boolean
    printedAt?: Date
    printerName?: string
    error?: string
  }
  cloud: {
    uploaded: boolean
    uploadedAt?: Date
    url?: string
    error?: string
  }
}

export interface BatchReportGeneration {
  id: string
  name: string
  description: string
  reports: Array<{
    templateId: string
    parameters: ReportParameters
    priority: number
  }>
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: {
    total: number
    completed: number
    failed: number
    current?: string
  }
  createdBy: string
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
  results: Array<{
    templateId: string
    reportId?: string
    status: ReportStatus
    error?: string
  }>
}

export interface ReportPreview {
  id: string
  reportType: PhilippineReportType
  templateId: string
  parameters: ReportParameters
  previewData: {
    pageCount: number
    samplePages: string[] // Base64 encoded images
    dataRows: number
    estimatedFileSize: number
    generationTime: number
  }
  generatedAt: Date
  expiresAt: Date
}

export interface ReportAnalytics {
  totalReports: number
  reportsByType: Record<PhilippineReportType, number>
  reportsByStatus: Record<ReportStatus, number>
  reportsByFormat: Record<ReportFormat, number>
  averageGenerationTime: number
  totalDownloads: number
  storageUsed: number
  popularTemplates: Array<{
    templateId: string
    name: string
    usageCount: number
  }>
  recentActivity: Array<{
    action: 'generated' | 'downloaded' | 'scheduled' | 'delivered'
    reportId: string
    reportName: string
    user: string
    timestamp: Date
  }>
}

export interface DepEDComplianceRule {
  id: string
  code: string
  title: string
  description: string
  category: 'format' | 'content' | 'signature' | 'timing' | 'distribution'
  severity: 'error' | 'warning' | 'info'
  applicableReports: PhilippineReportType[]
  validationFunction: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ReportAuditLog {
  id: string
  reportId: string
  action: 'created' | 'generated' | 'downloaded' | 'modified' | 'deleted' | 'shared' | 'signed'
  userId: string
  userName: string
  timestamp: Date
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export interface ReportPermission {
  id: string
  userId: string
  reportType: PhilippineReportType
  permissions: {
    view: boolean
    generate: boolean
    download: boolean
    schedule: boolean
    share: boolean
    delete: boolean
    sign: boolean
  }
  restrictions: {
    dateRange?: {
      start: Date
      end: Date
    }
    grades?: string[]
    sections?: string[]
    maxReportsPerDay?: number
  }
  grantedBy: string
  grantedAt: Date
  expiresAt?: Date
}

export interface ReportNotification {
  id: string
  type: 'generation_complete' | 'generation_failed' | 'scheduled_report' | 'compliance_issue' | 'storage_warning'
  title: string
  message: string
  reportId?: string
  userId: string
  isRead: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: Date
  readAt?: Date
  actionUrl?: string
}

export interface ReportFormData {
  reportType: PhilippineReportType
  templateId?: string
  name: string
  description?: string
  parameters: {
    dateRange: {
      start: string
      end: string
    }
    filters: {
      grades: string[]
      sections: string[]
      courses: string[]
      teachers: string[]
    }
    format: ReportFormat
    includeCharts: boolean
    includeStatistics: boolean
    includeSignatures: boolean
  }
  scheduling?: {
    enabled: boolean
    frequency?: 'daily' | 'weekly' | 'monthly'
    time?: string
    recipients?: string[]
  }
  delivery?: {
    email: {
      enabled: boolean
      recipients: string[]
      subject?: string
      message?: string
    }
    print: {
      enabled: boolean
      copies: number
    }
  }
}

// ===== PRISMA DATABASE TYPES =====
// Import Prisma generated types
export type {
  Student as PrismaStudent,
  Teacher as PrismaTeacher,
  Attendance as PrismaAttendance,
  Class as PrismaClass,
  SMSLog as PrismaSMSLog,
  QRCode as PrismaQRCode,
  SystemSetting as PrismaSystemSetting,
  StudentStatus as PrismaStudentStatus,
  TeacherStatus as PrismaTeacherStatus,
  AttendanceStatus as PrismaAttendanceStatus,
  SMSStatus as PrismaSMSStatus
} from '../generated/prisma'

// Extended Prisma types with relations
export interface PrismaStudentWithRelations {
  id: string
  studentNumber: string
  firstName: string
  lastName: string
  middleName?: string
  gradeLevel: string
  section: string
  guardianName: string
  guardianContact: string
  address: string
  profilePhoto?: string
  dateEnrolled: Date
  status: PrismaStudentStatus
  createdAt: Date
  updatedAt: Date
  attendances?: PrismaAttendance[]
  qrCodes?: PrismaQRCode[]
  smsLogs?: PrismaSMSLog[]
}

export interface PrismaTeacherWithRelations {
  id: string
  employeeNumber: string
  firstName: string
  lastName: string
  middleName?: string
  contactNumber: string
  email: string
  subjectsHandled: string
  advisorySection?: string
  status: PrismaTeacherStatus
  createdAt: Date
  updatedAt: Date
  attendances?: PrismaAttendance[]
  classes?: PrismaClass[]
}

export interface PrismaAttendanceWithRelations {
  id: string
  studentId: string
  teacherId: string
  date: Date
  timeIn?: Date
  timeOut?: Date
  status: PrismaAttendanceStatus
  remarks?: string
  createdAt: Date
  updatedAt: Date
  student?: PrismaStudent
  teacher?: PrismaTeacher
  smsLogs?: PrismaSMSLog[]
}

// Database utility types
export interface DatabaseHealthCheck {
  status: 'healthy' | 'unhealthy'
  error?: string
  timestamp: string
}

export interface PrismaAttendanceStats {
  total: number
  present: number
  absent: number
  late: number
  excused: number
  attendanceRate: number
}

export interface PrismaQRValidationResult {
  valid: boolean
  reason?: string
  qrCode?: {
    id: string
    studentId: string
    qrData: string
    generatedAt: Date
    expiresAt?: Date
    isActive: boolean
    student?: PrismaStudent
  }
}

// Form data types for Prisma models
export interface PrismaStudentFormData {
  studentNumber: string
  firstName: string
  lastName: string
  middleName?: string
  gradeLevel: string
  section: string
  guardianName: string
  guardianContact: string
  address: string
  profilePhoto?: string
  status?: PrismaStudentStatus
}

export interface PrismaTeacherFormData {
  employeeNumber: string
  firstName: string
  lastName: string
  middleName?: string
  contactNumber: string
  email: string
  subjectsHandled: string[]
  advisorySection?: string
  status?: PrismaTeacherStatus
}

export interface PrismaAttendanceFormData {
  studentId: string
  teacherId: string
  status: PrismaAttendanceStatus
  timeIn?: Date
  timeOut?: Date
  remarks?: string
}

export interface PrismaClassFormData {
  subjectName: string
  teacherId: string
  gradeLevel: string
  section: string
  scheduleTime: {
    days: string[]
    startTime: string
    endTime: string
  }
  roomNumber: string
}

// System configuration from Prisma
export interface PrismaSystemConfig {
  schoolName: string
  schoolAddress: string
  attendanceCutoffTime: string
  smsEnabled: boolean
  qrCodeExpiryHours: number
  autoSMSAbsent: boolean
}

// Filter types for Prisma queries
export interface PrismaStudentFilters {
  gradeLevel?: string
  section?: string
  status?: PrismaStudentStatus
  search?: string
}

export interface PrismaTeacherFilters {
  status?: PrismaTeacherStatus
  search?: string
  subject?: string
}

export interface PrismaAttendanceFilters {
  startDate?: Date
  endDate?: Date
  gradeLevel?: string
  section?: string
  status?: PrismaAttendanceStatus
  studentId?: string
  teacherId?: string
}

export interface PrismaSMSFilters {
  status?: PrismaSMSStatus
  startDate?: Date
  endDate?: Date
  studentId?: string
}
