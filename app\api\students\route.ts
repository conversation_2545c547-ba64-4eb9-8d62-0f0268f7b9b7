import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  studentQuerySchema,
  createStudentSchema,
  type CreateStudentInput
} from '@/lib/validations/student'
import {
  studentDbUtils,
  transformUtils
} from '@/lib/utils/student'

/**
 * GET /api/students
 * List students with pagination, search, filtering, and sorting
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'students-list', 100, 60 * 1000) // 100 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = studentQuerySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const query = validationResult.data

    // Build database query
    const where = studentDbUtils.buildWhereClause(query)
    const include = studentDbUtils.buildIncludeClause(query)
    const orderBy = studentDbUtils.buildOrderByClause(query)

    // Calculate pagination
    const skip = (query.page - 1) * query.limit

    // Execute queries
    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include,
        orderBy,
        skip,
        take: query.limit
      }),
      prisma.student.count({ where })
    ])

    // Transform data for response
    const transformedStudents = transformUtils.transformStudentsForResponse(students)
    const pagination = transformUtils.calculatePagination(query.page, query.limit, total)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'students',
      details: {
        query: queryParams,
        resultCount: students.length,
        totalCount: total
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedStudents,
      pagination,
      meta: {
        total,
        page: query.page,
        limit: query.limit,
        filters: {
          search: query.search,
          gradeLevel: query.gradeLevel,
          section: query.section,
          status: query.status
        },
        sorting: {
          sortBy: query.sortBy,
          sortOrder: query.sortOrder
        }
      }
    })

  } catch (error) {
    console.error('GET /api/students error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/students
 * Create a new student record
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'students-create', 10, 60 * 1000) // 10 creates per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can create students)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = createStudentSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const studentData: CreateStudentInput = validationResult.data

    // Check if student number already exists
    const existingStudent = await studentDbUtils.checkStudentNumberExists(studentData.studentNumber)
    if (existingStudent) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student number already exists',
          field: 'studentNumber'
        },
        { status: 409 }
      )
    }

    // Create student in database transaction
    const newStudent = await prisma.$transaction(async (tx) => {
      // Create the student
      const student = await tx.student.create({
        data: {
          studentNumber: studentData.studentNumber,
          firstName: studentData.firstName,
          lastName: studentData.lastName,
          middleName: studentData.middleName,
          gradeLevel: studentData.gradeLevel,
          section: studentData.section,
          guardianName: studentData.guardianName,
          guardianContact: studentData.guardianContact,
          address: studentData.address,
          status: studentData.status || 'ACTIVE',
          dateEnrolled: new Date()
        }
      })

      // Generate initial QR code
      const qrData = `QRSAMS_${student.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      await tx.qRCode.create({
        data: {
          studentId: student.id,
          qrData,
          isActive: true,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        }
      })

      return student
    })

    // Transform response data
    const transformedStudent = transformUtils.transformStudentForResponse(newStudent)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'student',
      resourceId: newStudent.id,
      details: {
        studentNumber: newStudent.studentNumber,
        name: `${newStudent.firstName} ${newStudent.lastName}`,
        gradeLevel: newStudent.gradeLevel,
        section: newStudent.section
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedStudent,
      message: 'Student created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/students error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT /api/students/[id] instead.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use DELETE /api/students/[id] instead.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT /api/students/[id] instead.' },
    { status: 405 }
  )
}