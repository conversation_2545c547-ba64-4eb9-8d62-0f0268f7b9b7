import { prisma } from '../db'
import { StudentStatus } from '../../generated/prisma'
import { StudentQueryInput } from '../validations/student'
import crypto from 'crypto'
import path from 'path'
import fs from 'fs/promises'

// Student database utilities
export const studentDbUtils = {
  // Check if student number exists
  checkStudentNumberExists: async (studentNumber: string, excludeId?: string) => {
    const existing = await prisma.student.findUnique({
      where: { studentNumber },
      select: { id: true }
    })

    if (!existing) return false
    if (excludeId && existing.id === excludeId) return false
    return true
  },

  // Build where clause for student queries
  buildWhereClause: (query: StudentQueryInput) => {
    const where: any = {}

    // Search across multiple fields
    if (query.search) {
      const searchTerm = query.search.toLowerCase()
      where.OR = [
        { firstName: { contains: searchTerm, mode: 'insensitive' } },
        { lastName: { contains: searchTerm, mode: 'insensitive' } },
        { middleName: { contains: searchTerm, mode: 'insensitive' } },
        { studentNumber: { contains: searchTerm, mode: 'insensitive' } },
        { guardianName: { contains: searchTerm, mode: 'insensitive' } }
      ]
    }

    // Filter by grade level
    if (query.gradeLevel) {
      where.gradeLevel = query.gradeLevel
    }

    // Filter by section
    if (query.section) {
      where.section = query.section
    }

    // Filter by status
    if (query.status) {
      where.status = query.status
    }

    return where
  },

  // Build include clause for related data
  buildIncludeClause: (query: StudentQueryInput) => {
    const include: any = {}

    if (query.includeAttendance) {
      include.attendances = {
        orderBy: { date: 'desc' },
        take: 10,
        include: {
          teacher: {
            select: {
              firstName: true,
              lastName: true,
              employeeNumber: true
            }
          }
        }
      }
    }

    if (query.includeQRCodes) {
      include.qrCodes = {
        where: { isActive: true },
        orderBy: { generatedAt: 'desc' },
        take: 5
      }
    }

    if (query.includeSMSLogs) {
      include.smsLogs = {
        orderBy: { createdAt: 'desc' },
        take: 10
      }
    }

    return include
  },

  // Build order by clause
  buildOrderByClause: (query: StudentQueryInput) => {
    const orderBy: any = {}

    switch (query.sortBy) {
      case 'firstName':
      case 'lastName':
      case 'studentNumber':
      case 'gradeLevel':
      case 'section':
      case 'dateEnrolled':
      case 'createdAt':
      case 'updatedAt':
        orderBy[query.sortBy] = query.sortOrder
        break
      default:
        orderBy.lastName = 'asc'
    }

    return orderBy
  },

  // Get student statistics
  getStudentStats: async (filters?: {
    gradeLevel?: string
    section?: string
    status?: StudentStatus
  }) => {
    const where: any = {}

    if (filters?.gradeLevel) where.gradeLevel = filters.gradeLevel
    if (filters?.section) where.section = filters.section
    if (filters?.status) where.status = filters.status

    const [total, active, inactive, graduated, transferred, dropped] = await Promise.all([
      prisma.student.count({ where }),
      prisma.student.count({ where: { ...where, status: 'ACTIVE' } }),
      prisma.student.count({ where: { ...where, status: 'INACTIVE' } }),
      prisma.student.count({ where: { ...where, status: 'GRADUATED' } }),
      prisma.student.count({ where: { ...where, status: 'TRANSFERRED' } }),
      prisma.student.count({ where: { ...where, status: 'DROPPED' } })
    ])

    return {
      total,
      active,
      inactive,
      graduated,
      transferred,
      dropped,
      distribution: {
        active: total > 0 ? (active / total) * 100 : 0,
        inactive: total > 0 ? (inactive / total) * 100 : 0,
        graduated: total > 0 ? (graduated / total) * 100 : 0,
        transferred: total > 0 ? (transferred / total) * 100 : 0,
        dropped: total > 0 ? (dropped / total) * 100 : 0
      }
    }
  }
}

// QR Code utilities
export const qrCodeUtils = {
  // Generate QR code data
  generateQRData: (studentId: string) => {
    const timestamp = Date.now()
    const random = crypto.randomBytes(8).toString('hex')
    return `QRSAMS_${studentId}_${timestamp}_${random}`
  },

  // Validate QR code format
  validateQRFormat: (qrData: string) => {
    const pattern = /^QRSAMS_[a-zA-Z0-9]+_\d+_[a-f0-9]{16}$/
    return pattern.test(qrData)
  },

  // Extract student ID from QR code
  extractStudentId: (qrData: string) => {
    if (!qrCodeUtils.validateQRFormat(qrData)) return null
    const parts = qrData.split('_')
    return parts.length >= 2 ? parts[1] : null
  },

  // Generate QR code for student
  generateForStudent: async (studentId: string, expiryHours: number = 24) => {
    // Deactivate existing QR codes
    await prisma.qRCode.updateMany({
      where: { studentId, isActive: true },
      data: { isActive: false }
    })

    // Generate new QR code
    const qrData = qrCodeUtils.generateQRData(studentId)
    const expiresAt = new Date(Date.now() + expiryHours * 60 * 60 * 1000)

    return await prisma.qRCode.create({
      data: {
        studentId,
        qrData,
        expiresAt,
        isActive: true
      },
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true,
            gradeLevel: true,
            section: true
          }
        }
      }
    })
  }
}

// File handling utilities
export const fileUtils = {
  // Validate file type
  validateFileType: (file: File, allowedTypes: string[]) => {
    return allowedTypes.includes(file.type)
  },

  // Validate file size
  validateFileSize: (file: File, maxSizeInMB: number) => {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024
    return file.size <= maxSizeInBytes
  },

  // Generate unique filename
  generateUniqueFilename: (originalName: string) => {
    const ext = path.extname(originalName)
    const name = path.basename(originalName, ext)
    const timestamp = Date.now()
    const random = crypto.randomBytes(4).toString('hex')
    return `${name}_${timestamp}_${random}${ext}`
  },

  // Save uploaded file
  saveUploadedFile: async (file: File, uploadDir: string) => {
    const filename = fileUtils.generateUniqueFilename(file.name)
    const filepath = path.join(uploadDir, filename)

    // Ensure upload directory exists
    await fs.mkdir(uploadDir, { recursive: true })

    // Save file
    const buffer = Buffer.from(await file.arrayBuffer())
    await fs.writeFile(filepath, buffer)

    return {
      filename,
      filepath,
      size: file.size,
      type: file.type
    }
  },

  // Delete file
  deleteFile: async (filepath: string) => {
    try {
      await fs.unlink(filepath)
      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      return false
    }
  }
}

// CSV processing utilities
export const csvUtils = {
  // Parse CSV content
  parseCSV: (content: string, hasHeader: boolean = true) => {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length === 0) return { headers: [], rows: [] }

    const headers = hasHeader ? lines[0].split(',').map(h => h.trim()) : []
    const dataLines = hasHeader ? lines.slice(1) : lines

    const rows = dataLines.map(line => {
      const values = line.split(',').map(v => v.trim())
      if (hasHeader) {
        const row: Record<string, string> = {}
        headers.forEach((header, index) => {
          row[header] = values[index] || ''
        })
        return row
      }
      return values
    })

    return { headers, rows }
  },

  // Validate CSV row for student data
  validateCSVRow: (row: Record<string, string>, mapping?: Record<string, string>) => {
    const errors: string[] = []
    const mappedRow: Record<string, string> = {}

    // Apply column mapping if provided
    if (mapping) {
      Object.entries(mapping).forEach(([csvColumn, dbField]) => {
        mappedRow[dbField] = row[csvColumn] || ''
      })
    } else {
      Object.assign(mappedRow, row)
    }

    // Required fields validation
    const requiredFields = ['studentNumber', 'firstName', 'lastName', 'gradeLevel', 'section', 'guardianName', 'guardianContact', 'address']

    requiredFields.forEach(field => {
      if (!mappedRow[field] || mappedRow[field].trim() === '') {
        errors.push(`${field} is required`)
      }
    })

    // Format validation
    if (mappedRow.guardianContact && !/^(\+63|0)[0-9]{10}$/.test(mappedRow.guardianContact)) {
      errors.push('Invalid guardian contact format')
    }

    return { isValid: errors.length === 0, errors, data: mappedRow }
  },

  // Convert students to CSV
  convertToCSV: (students: any[], fields?: string[]) => {
    if (students.length === 0) return ''

    const defaultFields = [
      'studentNumber', 'firstName', 'lastName', 'middleName',
      'gradeLevel', 'section', 'guardianName', 'guardianContact',
      'address', 'status', 'dateEnrolled', 'createdAt'
    ]

    const selectedFields = fields || defaultFields
    const headers = selectedFields.join(',')

    const rows = students.map(student => {
      return selectedFields.map(field => {
        let value = student[field]
        if (value instanceof Date) {
          value = value.toISOString().split('T')[0]
        }
        return `"${value || ''}"`
      }).join(',')
    })

    return [headers, ...rows].join('\n')
  }
}

// Data transformation utilities
export const transformUtils = {
  // Transform student data for API response
  transformStudentForResponse: (student: any) => {
    return {
      id: student.id,
      studentNumber: student.studentNumber,
      firstName: student.firstName,
      lastName: student.lastName,
      middleName: student.middleName,
      fullName: `${student.firstName} ${student.middleName ? student.middleName + ' ' : ''}${student.lastName}`,
      gradeLevel: student.gradeLevel,
      section: student.section,
      guardianName: student.guardianName,
      guardianContact: student.guardianContact,
      address: student.address,
      profilePhoto: student.profilePhoto,
      status: student.status,
      dateEnrolled: student.dateEnrolled,
      createdAt: student.createdAt,
      updatedAt: student.updatedAt,
      attendances: student.attendances || [],
      qrCodes: student.qrCodes || [],
      smsLogs: student.smsLogs || []
    }
  },

  // Transform multiple students
  transformStudentsForResponse: (students: any[]) => {
    return students.map(transformUtils.transformStudentForResponse)
  },

  // Calculate pagination metadata
  calculatePagination: (page: number, limit: number, total: number) => {
    const totalPages = Math.ceil(total / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    return {
      page,
      limit,
      total,
      totalPages,
      hasNextPage,
      hasPrevPage,
      nextPage: hasNextPage ? page + 1 : null,
      prevPage: hasPrevPage ? page - 1 : null
    }
  }
}