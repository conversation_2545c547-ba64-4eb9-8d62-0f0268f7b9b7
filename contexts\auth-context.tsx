"use client"

import React, { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState, ReactNode } from 'react'
import { UserRole } from '../generated/prisma'

// User interface
export interface User {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  lastLoginAt: Date | null
}

// Auth context interface
interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (username: string, password: string, rememberMe?: boolean) => Promise<LoginResult>
  logout: () => Promise<void>
  refreshToken: () => Promise<boolean>
  checkAuth: () => Promise<void>
}

// Login result interface
interface LoginResult {
  success: boolean
  error?: string
  user?: User
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth provider props
interface AuthProviderProps {
  children: ReactNode
}

// Get CSRF token from cookie
const getCSRFToken = (): string | null => {
  if (typeof document === 'undefined') return null
  
  const cookies = document.cookie.split(';')
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'csrf-token') {
      return decodeURIComponent(value)
    }
  }
  return null
}

// API request helper with CSRF protection
const apiRequest = async (url: string, options: RequestInit = {}) => {
  const csrfToken = getCSRFToken()
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers
  }
  
  if (csrfToken && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method || 'GET')) {
    headers['X-CSRF-Token'] = csrfToken
  }
  
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include' // Include cookies
  })
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Check authentication status
  const checkAuth = async () => {
    try {
      setIsLoading(true)
      const response = await apiRequest('/api/auth/me')
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.user) {
          setUser(data.user)
          setIsAuthenticated(true)
        } else {
          setUser(null)
          setIsAuthenticated(false)
        }
      } else {
        setUser(null)
        setIsAuthenticated(false)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  // Login function
  const login = async (username: string, password: string, rememberMe: boolean = false): Promise<LoginResult> => {
    try {
      setIsLoading(true)
      
      const response = await apiRequest('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password, rememberMe })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setUser(data.user)
        setIsAuthenticated(true)
        return { success: true, user: data.user }
      } else {
        return { success: false, error: data.error || 'Login failed' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error occurred' }
    } finally {
      setIsLoading(false)
    }
  }

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true)
      
      await apiRequest('/api/auth/logout', {
        method: 'POST'
      })
      
      setUser(null)
      setIsAuthenticated(false)
      
      // Redirect to login page
      window.location.href = '/login'
    } catch (error) {
      console.error('Logout error:', error)
      // Even if logout API fails, clear local state
      setUser(null)
      setIsAuthenticated(false)
      window.location.href = '/login'
    } finally {
      setIsLoading(false)
    }
  }

  // Refresh token function
  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await apiRequest('/api/auth/refresh', {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        return data.success
      }
      
      return false
    } catch (error) {
      console.error('Token refresh error:', error)
      return false
    }
  }

  // Auto-refresh token before expiration
  useEffect(() => {
    if (!isAuthenticated) return

    // Set up token refresh interval (refresh every 10 minutes)
    const refreshInterval = setInterval(async () => {
      const success = await refreshToken()
      if (!success) {
        // Refresh failed, user needs to login again
        setUser(null)
        setIsAuthenticated(false)
        window.location.href = '/login'
      }
    }, 10 * 60 * 1000) // 10 minutes

    return () => clearInterval(refreshInterval)
  }, [isAuthenticated])

  // Check auth on mount
  useEffect(() => {
    checkAuth()
  }, [])

  // Handle visibility change to refresh auth when tab becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        checkAuth()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [isAuthenticated])

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshToken,
    checkAuth
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook to check if user has specific role
export const useRole = (requiredRole: UserRole | UserRole[]): boolean => {
  const { user } = useAuth()
  
  if (!user) return false
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(user.role)
  }
  
  return user.role === requiredRole
}

// Hook to check if user has admin role
export const useIsAdmin = (): boolean => {
  return useRole(UserRole.ADMIN)
}

// Hook to check if user has teacher role
export const useIsTeacher = (): boolean => {
  return useRole([UserRole.ADMIN, UserRole.TEACHER])
}

// Higher-order component for role-based access
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: UserRole | UserRole[]
) => {
  return function AuthenticatedComponent(props: P) {
    const { isLoading, isAuthenticated, user } = useAuth()
    
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      )
    }
    
    if (!isAuthenticated) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">Please log in to access this page.</p>
          </div>
        </div>
      )
    }
    
    if (requiredRole && user) {
      const hasRole = Array.isArray(requiredRole) 
        ? requiredRole.includes(user.role)
        : user.role === requiredRole
        
      if (!hasRole) {
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Insufficient Permissions</h1>
              <p className="text-gray-600">You don't have permission to access this page.</p>
            </div>
          </div>
        )
      }
    }
    
    return <Component {...props} />
  }
}
