"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/index.js":
/*!****************************************!*\
  !*** ./node_modules/bcryptjs/index.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   compareSync: () => (/* binding */ compareSync),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64),\n/* harmony export */   genSalt: () => (/* binding */ genSalt),\n/* harmony export */   genSaltSync: () => (/* binding */ genSaltSync),\n/* harmony export */   getRounds: () => (/* binding */ getRounds),\n/* harmony export */   getSalt: () => (/* binding */ getSalt),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   hashSync: () => (/* binding */ hashSync),\n/* harmony export */   setRandomFallback: () => (/* binding */ setRandomFallback),\n/* harmony export */   truncates: () => (/* binding */ truncates)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/*\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\n Copyright (c) 2012 Shane Girish <<EMAIL>>\n Copyright (c) 2025 Daniel Wirtz <<EMAIL>>\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions\n are met:\n 1. Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n 2. Redistributions in binary form must reproduce the above copyright\n notice, this list of conditions and the following disclaimer in the\n documentation and/or other materials provided with the distribution.\n 3. The name of the author may not be used to endorse or promote products\n derived from this software without specific prior written permission.\n\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n// The Node.js crypto module is used as a fallback for the Web Crypto API. When\n// building for the browser, inclusion of the crypto module should be disabled,\n// which the package hints at in its package.json for bundlers that support it.\n\n\n/**\n * The random implementation to use as a fallback.\n * @type {?function(number):!Array.<number>}\n * @inner\n */\nvar randomFallback = null;\n\n/**\n * Generates cryptographically secure random bytes.\n * @function\n * @param {number} len Bytes length\n * @returns {!Array.<number>} Random bytes\n * @throws {Error} If no random implementation is available\n * @inner\n */\nfunction randomBytes(len) {\n  // Web Crypto API. Globally available in the browser and in Node.js >=23.\n  try {\n    return crypto.getRandomValues(new Uint8Array(len));\n  } catch {}\n  // Node.js crypto module for non-browser environments.\n  try {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(len);\n  } catch {}\n  // Custom fallback specified with `setRandomFallback`.\n  if (!randomFallback) {\n    throw Error(\n      \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n    );\n  }\n  return randomFallback(len);\n}\n\n/**\n * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n *  is seeded properly!\n * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n * @see http://nodejs.org/api/crypto.html\n * @see http://www.w3.org/TR/WebCryptoAPI/\n */\nfunction setRandomFallback(random) {\n  randomFallback = random;\n}\n\n/**\n * Synchronously generates a salt.\n * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {number=} seed_length Not supported.\n * @returns {string} Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nfunction genSaltSync(rounds, seed_length) {\n  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof rounds !== \"number\")\n    throw Error(\n      \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n    );\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n  var salt = [];\n  salt.push(\"$2b$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n  return salt.join(\"\");\n}\n\n/**\n * Asynchronously generates a salt.\n * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {(number|function(Error, string=))=} seed_length Not supported.\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction genSalt(rounds, seed_length, callback) {\n  if (typeof seed_length === \"function\")\n    (callback = seed_length), (seed_length = undefined); // Not supported.\n  if (typeof rounds === \"function\") (callback = rounds), (rounds = undefined);\n  if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n  else if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  function _async(callback) {\n    nextTick(function () {\n      // Pretty thin, but salting is fast enough\n      try {\n        callback(null, genSaltSync(rounds));\n      } catch (err) {\n        callback(err);\n      }\n    });\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Synchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n * @returns {string} Resulting hash\n */\nfunction hashSync(password, salt) {\n  if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof password !== \"string\" || typeof salt !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n  return _hash(password, salt);\n}\n\n/**\n * Asynchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {number|string} salt Salt length to generate or salt to use\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction hash(password, salt, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password === \"string\" && typeof salt === \"number\")\n      genSalt(salt, function (err, salt) {\n        _hash(password, salt, callback, progressCallback);\n      });\n    else if (typeof password === \"string\" && typeof salt === \"string\")\n      _hash(password, salt, callback, progressCallback);\n    else\n      nextTick(\n        callback.bind(\n          this,\n          Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt),\n        ),\n      );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Compares two strings of the same length in constant time.\n * @param {string} known Must be of the correct length\n * @param {string} unknown Must be the same length as `known`\n * @returns {boolean}\n * @inner\n */\nfunction safeStringCompare(known, unknown) {\n  var diff = known.length ^ unknown.length;\n  for (var i = 0; i < known.length; ++i) {\n    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n  }\n  return diff === 0;\n}\n\n/**\n * Synchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hash Hash to test against\n * @returns {boolean} true if matching, otherwise false\n * @throws {Error} If an argument is illegal\n */\nfunction compareSync(password, hash) {\n  if (typeof password !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n  return safeStringCompare(\n    hashSync(password, hash.substring(0, hash.length - 31)),\n    hash,\n  );\n}\n\n/**\n * Asynchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hashValue Hash to test against\n * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction compare(password, hashValue, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n      nextTick(\n        callback.bind(\n          this,\n          Error(\n            \"Illegal arguments: \" + typeof password + \", \" + typeof hashValue,\n          ),\n        ),\n      );\n      return;\n    }\n    if (hashValue.length !== 60) {\n      nextTick(callback.bind(this, null, false));\n      return;\n    }\n    hash(\n      password,\n      hashValue.substring(0, 29),\n      function (err, comp) {\n        if (err) callback(err);\n        else callback(null, safeStringCompare(comp, hashValue));\n      },\n      progressCallback,\n    );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Gets the number of rounds used to encrypt the specified hash.\n * @param {string} hash Hash to extract the used number of rounds from\n * @returns {number} Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nfunction getRounds(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  return parseInt(hash.split(\"$\")[2], 10);\n}\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n * @param {string} hash Hash to extract the salt from\n * @returns {string} Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nfunction getSalt(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  if (hash.length !== 60)\n    throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n  return hash.substring(0, 29);\n}\n\n/**\n * Tests if a password will be truncated when hashed, that is its length is\n * greater than 72 bytes when converted to UTF-8.\n * @param {string} password The password to test\n * @returns {boolean} `true` if truncated, otherwise `false`\n */\nfunction truncates(password) {\n  if (typeof password !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password);\n  return utf8Length(password) > 72;\n}\n\n/**\n * Continues with the callback on the next tick.\n * @function\n * @param {function(...[*])} callback Callback to execute\n * @inner\n */\nvar nextTick =\n  typeof process !== \"undefined\" &&\n  process &&\n  typeof process.nextTick === \"function\"\n    ? typeof setImmediate === \"function\"\n      ? setImmediate\n      : process.nextTick\n    : setTimeout;\n\n/** Calculates the byte length of a string encoded as UTF8. */\nfunction utf8Length(string) {\n  var len = 0,\n    c = 0;\n  for (var i = 0; i < string.length; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128) len += 1;\n    else if (c < 2048) len += 2;\n    else if (\n      (c & 0xfc00) === 0xd800 &&\n      (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      ++i;\n      len += 4;\n    } else len += 3;\n  }\n  return len;\n}\n\n/** Converts a string to an array of UTF8 bytes. */\nfunction utf8Array(string) {\n  var offset = 0,\n    c1,\n    c2;\n  var buffer = new Array(utf8Length(string));\n  for (var i = 0, k = string.length; i < k; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = (c1 >> 6) | 192;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else if (\n      (c1 & 0xfc00) === 0xd800 &&\n      ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n    ) {\n      c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n      ++i;\n      buffer[offset++] = (c1 >> 18) | 240;\n      buffer[offset++] = ((c1 >> 12) & 63) | 128;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else {\n      buffer[offset++] = (c1 >> 12) | 224;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    }\n  }\n  return buffer;\n}\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n/**\n * bcrypt's own non-standard base64 dictionary.\n * @type {!Array.<string>}\n * @const\n * @inner\n **/\nvar BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\n/**\n * @type {!Array.<number>}\n * @const\n * @inner\n **/\nvar BASE64_INDEX = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,\n  -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28,\n  29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\n  48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n];\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input.\n * @param {!Array.<number>} b Byte array\n * @param {number} len Maximum input length\n * @returns {string}\n * @inner\n */\nfunction base64_encode(b, len) {\n  var off = 0,\n    rs = [],\n    c1,\n    c2;\n  if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n  while (off < len) {\n    c1 = b[off++] & 0xff;\n    rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    rs.push(BASE64_CODE[c2 & 0x3f]);\n  }\n  return rs.join(\"\");\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output.\n * @param {string} s String to decode\n * @param {number} len Maximum output length\n * @returns {!Array.<number>}\n * @inner\n */\nfunction base64_decode(s, len) {\n  var off = 0,\n    slen = s.length,\n    olen = 0,\n    rs = [],\n    c1,\n    c2,\n    c3,\n    c4,\n    o,\n    code;\n  if (len <= 0) throw Error(\"Illegal len: \" + len);\n  while (off < slen - 1 && olen < len) {\n    code = s.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = s.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c1 == -1 || c2 == -1) break;\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    rs.push(String.fromCharCode(o));\n    ++olen;\n  }\n  var res = [];\n  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n  return res;\n}\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BCRYPT_SALT_LEN = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BLOWFISH_NUM_ROUNDS = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar MAX_EXECUTION_TIME = 100;\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n\n/**\n * @param {Array.<number>} lr\n * @param {number} off\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @returns {Array.<number>}\n * @inner\n */\nfunction _encipher(lr, off, P, S) {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  var n,\n    l = lr[off],\n    r = lr[off + 1];\n\n  l ^= P[0];\n\n  /*\n    for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n        // Feistel substitution on left word\n        n  = S[l >>> 24],\n        n += S[0x100 | ((l >> 16) & 0xff)],\n        n ^= S[0x200 | ((l >> 8) & 0xff)],\n        n += S[0x300 | (l & 0xff)],\n        r ^= n ^ P[++i],\n        // Feistel substitution on right word\n        n  = S[r >>> 24],\n        n += S[0x100 | ((r >> 16) & 0xff)],\n        n ^= S[0x200 | ((r >> 8) & 0xff)],\n        n += S[0x300 | (r & 0xff)],\n        l ^= n ^ P[++i];\n    */\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n  return lr;\n}\n\n/**\n * @param {Array.<number>} data\n * @param {number} offp\n * @returns {{key: number, offp: number}}\n * @inner\n */\nfunction _streamtoword(data, offp) {\n  for (var i = 0, word = 0; i < 4; ++i)\n    (word = (word << 8) | (data[offp] & 0xff)),\n      (offp = (offp + 1) % data.length);\n  return { key: word, offp: offp };\n}\n\n/**\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _key(key, P, S) {\n  var offset = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offset)),\n      (offset = sw.offp),\n      (P[i] = P[i] ^ sw.key);\n  for (i = 0; i < plen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n}\n\n/**\n * Expensive key schedule Blowfish.\n * @param {Array.<number>} data\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _ekskey(data, key, P, S) {\n  var offp = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offp)), (offp = sw.offp), (P[i] = P[i] ^ sw.key);\n  offp = 0;\n  for (i = 0; i < plen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (P[i] = lr[0]),\n      (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (S[i] = lr[0]),\n      (S[i + 1] = lr[1]);\n}\n\n/**\n * Internaly crypts a string.\n * @param {Array.<number>} b Bytes to crypt\n * @param {Array.<number>} salt Salt bytes to use\n * @param {number} rounds Number of rounds\n * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n *  omitted, the operation will be performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _crypt(b, salt, rounds, callback, progressCallback) {\n  var cdata = C_ORIG.slice(),\n    clen = cdata.length,\n    err;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    err = Error(\n      \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n    );\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  rounds = (1 << rounds) >>> 0;\n\n  var P,\n    S,\n    i = 0,\n    j;\n\n  //Use typed arrays when available - huge speedup!\n  if (typeof Int32Array === \"function\") {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  _ekskey(salt, b, P, S);\n\n  /**\n   * Calcualtes the next round.\n   * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n   * @inner\n   */\n  function next() {\n    if (progressCallback) progressCallback(i / rounds);\n    if (i < rounds) {\n      var start = Date.now();\n      for (; i < rounds; ) {\n        i = i + 1;\n        _key(b, P, S);\n        _key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n      var ret = [];\n      for (i = 0; i < clen; i++)\n        ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n          ret.push((cdata[i] & 0xff) >>> 0);\n      if (callback) {\n        callback(null, ret);\n        return;\n      } else return ret;\n    }\n    if (callback) nextTick(next);\n  }\n\n  // Async\n  if (typeof callback !== \"undefined\") {\n    next();\n\n    // Sync\n  } else {\n    var res;\n    while (true) if (typeof (res = next()) !== \"undefined\") return res || [];\n  }\n}\n\n/**\n * Internally hashes a password.\n * @param {string} password Password to hash\n * @param {?string} salt Salt to use, actually never null\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n *  hashing is performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _hash(password, salt, callback, progressCallback) {\n  var err;\n  if (typeof password !== \"string\" || typeof salt !== \"string\") {\n    err = Error(\"Invalid string / salt: Not a string\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n\n  // Validate the salt\n  var minor, offset;\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.charAt(2) === \"$\") (minor = String.fromCharCode(0)), (offset = 3);\n  else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n      if (callback) {\n        nextTick(callback.bind(this, err));\n        return;\n      } else throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    err = Error(\"Missing salt rounds\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    real_salt = salt.substring(offset + 3, offset + 25);\n  password += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  var passwordb = utf8Array(password),\n    saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param {Array.<number>} bytes Byte array\n   * @returns {string}\n   * @inner\n   */\n  function finish(bytes) {\n    var res = [];\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(base64_encode(saltb, saltb.length));\n    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n    return res.join(\"\");\n  }\n\n  // Sync\n  if (typeof callback == \"undefined\")\n    return finish(_crypt(passwordb, saltb, rounds));\n  // Async\n  else {\n    _crypt(\n      passwordb,\n      saltb,\n      rounds,\n      function (err, bytes) {\n        if (err) callback(err, null);\n        else callback(null, finish(bytes));\n      },\n      progressCallback,\n    );\n  }\n}\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n * @function\n * @param {!Array.<number>} bytes Byte array\n * @param {number} length Maximum input length\n * @returns {string}\n */\nfunction encodeBase64(bytes, length) {\n  return base64_encode(bytes, length);\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n * @function\n * @param {string} string String to decode\n * @param {number} length Maximum output length\n * @returns {!Array.<number>}\n */\nfunction decodeBase64(string, length) {\n  return base64_decode(string, length);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  setRandomFallback,\n  genSaltSync,\n  genSalt,\n  hashSync,\n  hash,\n  compareSync,\n  compare,\n  getRounds,\n  getSalt,\n  truncates,\n  encodeBase64,\n  decodeBase64,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/index.js\n");

/***/ })

};
;