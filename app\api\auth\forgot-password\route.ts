import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { passwordUtils } from '@/lib/auth'
import { withRateLimit, rateLimiters } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'
import { getClientIP, recordSuspiciousActivity } from '@/lib/security'

// Forgot password request schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address')
})

async function handler(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    // Parse and validate request body
    const body = await request.json()
    const validationResult = forgotPasswordSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email address',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { email } = validationResult.data

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { 
        email: email.toLowerCase(),
        isActive: true
      }
    })

    // Always return success to prevent email enumeration
    // But only send email if user exists
    if (user) {
      // Generate reset token
      const resetToken = passwordUtils.generateResetToken()
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

      // Save reset token to database
      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken: resetToken,
          passwordResetExpires: resetExpires
        }
      })

      // Log password reset request
      await auditHelpers.createAuditLog({
        userId: user.id,
        action: 'PASSWORD_RESET_REQUEST' as any,
        resource: 'authentication',
        details: { email },
        ipAddress: clientIP,
        userAgent: request.headers.get('user-agent') || 'unknown'
      })

      // In a real application, you would send an email here
      // For now, we'll just log it (in development, you might want to show the token)
      console.log(`Password reset token for ${email}: ${resetToken}`)
      
      // TODO: Implement email sending
      // await sendPasswordResetEmail(user.email, resetToken)
    } else {
      // Log suspicious activity for non-existent email
      recordSuspiciousActivity(clientIP, `Password reset attempt for non-existent email: ${email}`)
    }

    // Always return success message
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    })

  } catch (error) {
    console.error('Forgot password API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// Apply rate limiting (3 requests per hour)
export const POST = withRateLimit(handler, rateLimiters.passwordReset)

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
