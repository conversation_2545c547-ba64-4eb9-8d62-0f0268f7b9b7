import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  studentIdSchema,
  updateStudentSchema,
  attendanceSummarySchema,
  type UpdateStudentInput
} from '@/lib/validations/student'
import {
  studentDbUtils,
  transformUtils
} from '@/lib/utils/student'

/**
 * GET /api/students/[id]
 * Get individual student details with related data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'student-detail', 50, 60 * 1000) // 50 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Parse query parameters for additional options
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())
    const attendanceQuery = attendanceSummarySchema.safeParse(queryParams)

    // Find student with related data
    const student = await prisma.student.findUnique({
      where: { id: params.id },
      include: {
        attendances: {
          orderBy: { date: 'desc' },
          take: attendanceQuery.success && attendanceQuery.data.includeDetails ? 50 : 10,
          include: {
            teacher: {
              select: {
                firstName: true,
                lastName: true,
                employeeNumber: true
              }
            }
          },
          ...(attendanceQuery.success && (attendanceQuery.data.startDate || attendanceQuery.data.endDate) && {
            where: {
              date: {
                ...(attendanceQuery.data.startDate && { gte: attendanceQuery.data.startDate }),
                ...(attendanceQuery.data.endDate && { lte: attendanceQuery.data.endDate })
              }
            }
          })
        },
        qrCodes: {
          where: { isActive: true },
          orderBy: { generatedAt: 'desc' },
          take: 5
        },
        smsLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })

    if (!student) {
      return NextResponse.json(
        { success: false, error: 'Student not found' },
        { status: 404 }
      )
    }

    // Calculate attendance statistics if requested
    let attendanceStats = null
    if (attendanceQuery.success) {
      const { startDate, endDate } = attendanceQuery.data
      attendanceStats = await studentDbUtils.getStudentStats({
        gradeLevel: student.gradeLevel,
        section: student.section
      })
    }

    // Transform response data
    const transformedStudent = transformUtils.transformStudentForResponse(student)

    // Add attendance statistics if calculated
    if (attendanceStats) {
      transformedStudent.attendanceStats = attendanceStats
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'student',
      resourceId: student.id,
      details: {
        studentNumber: student.studentNumber,
        name: `${student.firstName} ${student.lastName}`,
        includeAttendanceDetails: attendanceQuery.success && attendanceQuery.data.includeDetails
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedStudent
    })

  } catch (error) {
    console.error('GET /api/students/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/students/[id]
 * Update student information
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'student-update', 20, 60 * 1000) // 20 updates per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can update students)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = updateStudentSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const updateData: UpdateStudentInput = validationResult.data

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id: params.id },
      select: { id: true, studentNumber: true, firstName: true, lastName: true }
    })

    if (!existingStudent) {
      return NextResponse.json(
        { success: false, error: 'Student not found' },
        { status: 404 }
      )
    }

    // Check if student number is being changed and if it already exists
    if (updateData.studentNumber && updateData.studentNumber !== existingStudent.studentNumber) {
      const numberExists = await studentDbUtils.checkStudentNumberExists(updateData.studentNumber, params.id)
      if (numberExists) {
        return NextResponse.json(
          {
            success: false,
            error: 'Student number already exists',
            field: 'studentNumber'
          },
          { status: 409 }
        )
      }
    }

    // Update student in database
    const updatedStudent = await prisma.student.update({
      where: { id: params.id },
      data: {
        ...(updateData.studentNumber && { studentNumber: updateData.studentNumber }),
        ...(updateData.firstName && { firstName: updateData.firstName }),
        ...(updateData.lastName && { lastName: updateData.lastName }),
        ...(updateData.middleName !== undefined && { middleName: updateData.middleName }),
        ...(updateData.gradeLevel && { gradeLevel: updateData.gradeLevel }),
        ...(updateData.section && { section: updateData.section }),
        ...(updateData.guardianName && { guardianName: updateData.guardianName }),
        ...(updateData.guardianContact && { guardianContact: updateData.guardianContact }),
        ...(updateData.address && { address: updateData.address }),
        ...(updateData.status && { status: updateData.status })
      },
      include: {
        attendances: {
          orderBy: { date: 'desc' },
          take: 5
        },
        qrCodes: {
          where: { isActive: true },
          orderBy: { generatedAt: 'desc' },
          take: 1
        }
      }
    })

    // Transform response data
    const transformedStudent = transformUtils.transformStudentForResponse(updatedStudent)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'UPDATE',
      resource: 'student',
      resourceId: updatedStudent.id,
      details: {
        studentNumber: updatedStudent.studentNumber,
        name: `${updatedStudent.firstName} ${updatedStudent.lastName}`,
        changes: updateData
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedStudent,
      message: 'Student updated successfully'
    })

  } catch (error) {
    console.error('PUT /api/students/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/students/[id]
 * Soft delete student record
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'student-delete', 10, 60 * 1000) // 10 deletes per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN can delete students)
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions. Only administrators can delete students.' },
        { status: 403 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        status: true
      }
    })

    if (!existingStudent) {
      return NextResponse.json(
        { success: false, error: 'Student not found' },
        { status: 404 }
      )
    }

    // Soft delete by updating status to INACTIVE
    const deletedStudent = await prisma.student.update({
      where: { id: params.id },
      data: {
        status: 'INACTIVE'
      }
    })

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'DELETE',
      resource: 'student',
      resourceId: deletedStudent.id,
      details: {
        studentNumber: deletedStudent.studentNumber,
        name: `${deletedStudent.firstName} ${deletedStudent.lastName}`,
        previousStatus: existingStudent.status,
        deletionType: 'soft'
      }
    }, request)

    return NextResponse.json({
      success: true,
      message: 'Student deleted successfully'
    })

  } catch (error) {
    console.error('DELETE /api/students/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST /api/students instead.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT /api/students/[id] instead.' },
    { status: 405 }
  )
}