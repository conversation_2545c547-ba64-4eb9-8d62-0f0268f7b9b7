import { PrismaClient } from '../generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  await prisma.auditLog.deleteMany()
  await prisma.sMSLog.deleteMany()
  await prisma.qRCode.deleteMany()
  await prisma.attendance.deleteMany()
  await prisma.class.deleteMany()
  await prisma.student.deleteMany()
  await prisma.teacher.deleteMany()
  await prisma.user.deleteMany()
  await prisma.systemSetting.deleteMany()

  // Seed Users (Authentication)
  console.log('👤 Seeding users...')
  const hashedPassword = await bcrypt.hash('password123', 12)

  const users = await prisma.user.createMany({
    data: [
      {
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'System',
        lastName: 'Administrator',
        role: '<PERSON><PERSON><PERSON>',
        isActive: true
      },
      {
        username: 'teacher1',
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Maria',
        lastName: 'Santos',
        middleName: 'Cruz',
        role: 'TEACHER',
        isActive: true
      },
      {
        username: 'teacher2',
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Juan',
        lastName: 'Dela Cruz',
        middleName: 'Reyes',
        role: 'TEACHER',
        isActive: true
      },
      {
        username: 'staff1',
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        firstName: 'Office',
        lastName: 'Staff',
        role: 'STAFF',
        isActive: true
      }
    ]
  })

  console.log(`✅ Created ${users.count} users`)
  console.log('📝 Default login credentials:')
  console.log('   Admin: admin / password123')
  console.log('   Teacher: teacher1 / password123')
  console.log('   Staff: staff1 / password123')

  // Seed System Settings
  console.log('📋 Seeding system settings...')
  await prisma.systemSetting.createMany({
    data: [
      {
        key: 'SCHOOL_NAME',
        value: 'Tanauan School of Arts and Trade',
        description: 'Official school name'
      },
      {
        key: 'SCHOOL_ADDRESS',
        value: 'Tanauan City, Batangas, Philippines',
        description: 'School address'
      },
      {
        key: 'ATTENDANCE_CUTOFF_TIME',
        value: '08:00',
        description: 'Time after which students are marked late'
      },
      {
        key: 'SMS_ENABLED',
        value: 'true',
        description: 'Enable SMS notifications'
      },
      {
        key: 'QR_CODE_EXPIRY_HOURS',
        value: '24',
        description: 'QR code expiry time in hours'
      },
      {
        key: 'AUTO_SMS_ABSENT',
        value: 'true',
        description: 'Automatically send SMS for absent students'
      }
    ]
  })

  // Seed Teachers
  console.log('👨‍🏫 Seeding teachers...')
  const teachers = await prisma.teacher.createMany({
    data: [
      {
        employeeNumber: 'EMP001',
        firstName: 'Maria',
        lastName: 'Santos',
        middleName: 'Cruz',
        contactNumber: '+639171234567',
        email: '<EMAIL>',
        subjectsHandled: JSON.stringify(['Mathematics', 'Statistics']),
        advisorySection: 'Grade 11-A',
        status: 'ACTIVE'
      },
      {
        employeeNumber: 'EMP002',
        firstName: 'Juan',
        lastName: 'Dela Cruz',
        middleName: 'Reyes',
        contactNumber: '+639181234567',
        email: '<EMAIL>',
        subjectsHandled: JSON.stringify(['English', 'Literature']),
        advisorySection: 'Grade 11-B',
        status: 'ACTIVE'
      },
      {
        employeeNumber: 'EMP003',
        firstName: 'Ana',
        lastName: 'Garcia',
        middleName: 'Lopez',
        contactNumber: '+639191234567',
        email: '<EMAIL>',
        subjectsHandled: JSON.stringify(['Science', 'Chemistry', 'Physics']),
        advisorySection: 'Grade 12-A',
        status: 'ACTIVE'
      },
      {
        employeeNumber: 'EMP004',
        firstName: 'Roberto',
        lastName: 'Mendoza',
        middleName: 'Torres',
        contactNumber: '+639201234567',
        email: '<EMAIL>',
        subjectsHandled: JSON.stringify(['History', 'Social Studies']),
        advisorySection: 'Grade 12-B',
        status: 'ACTIVE'
      },
      {
        employeeNumber: 'EMP005',
        firstName: 'Carmen',
        lastName: 'Villanueva',
        middleName: 'Ramos',
        contactNumber: '+639211234567',
        email: '<EMAIL>',
        subjectsHandled: JSON.stringify(['Computer Science', 'Programming']),
        status: 'ACTIVE'
      }
    ]
  })

  // Get created teachers for relations
  const teachersList = await prisma.teacher.findMany()

  // Seed Students
  console.log('👨‍🎓 Seeding students...')
  const students = await prisma.student.createMany({
    data: [
      {
        studentNumber: 'STU2024001',
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'Smith',
        gradeLevel: 'Grade 11',
        section: 'A',
        guardianName: 'Jane Doe',
        guardianContact: '+639301234567',
        address: '123 Main St, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024002',
        firstName: 'Maria',
        lastName: 'Cruz',
        middleName: 'Santos',
        gradeLevel: 'Grade 11',
        section: 'A',
        guardianName: 'Pedro Cruz',
        guardianContact: '+639311234567',
        address: '456 Oak Ave, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024003',
        firstName: 'Jose',
        lastName: 'Reyes',
        middleName: 'Garcia',
        gradeLevel: 'Grade 11',
        section: 'B',
        guardianName: 'Rosa Reyes',
        guardianContact: '+639321234567',
        address: '789 Pine St, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024004',
        firstName: 'Anna',
        lastName: 'Lopez',
        middleName: 'Torres',
        gradeLevel: 'Grade 12',
        section: 'A',
        guardianName: 'Miguel Lopez',
        guardianContact: '+639331234567',
        address: '321 Elm St, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024005',
        firstName: 'Carlos',
        lastName: 'Mendoza',
        middleName: 'Rivera',
        gradeLevel: 'Grade 12',
        section: 'B',
        guardianName: 'Elena Mendoza',
        guardianContact: '+639341234567',
        address: '654 Maple Ave, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024006',
        firstName: 'Sofia',
        lastName: 'Hernandez',
        middleName: 'Morales',
        gradeLevel: 'Grade 11',
        section: 'A',
        guardianName: 'Luis Hernandez',
        guardianContact: '+639351234567',
        address: '987 Cedar St, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024007',
        firstName: 'Miguel',
        lastName: 'Fernandez',
        middleName: 'Castro',
        gradeLevel: 'Grade 11',
        section: 'B',
        guardianName: 'Carmen Fernandez',
        guardianContact: '+639361234567',
        address: '147 Birch Ave, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024008',
        firstName: 'Isabella',
        lastName: 'Gonzalez',
        middleName: 'Vargas',
        gradeLevel: 'Grade 12',
        section: 'A',
        guardianName: 'Roberto Gonzalez',
        guardianContact: '+639371234567',
        address: '258 Spruce St, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024009',
        firstName: 'Diego',
        lastName: 'Ramirez',
        middleName: 'Silva',
        gradeLevel: 'Grade 12',
        section: 'B',
        guardianName: 'Patricia Ramirez',
        guardianContact: '+639381234567',
        address: '369 Willow Ave, Tanauan City, Batangas',
        status: 'ACTIVE'
      },
      {
        studentNumber: 'STU2024010',
        firstName: 'Valentina',
        lastName: 'Jimenez',
        middleName: 'Ortega',
        gradeLevel: 'Grade 11',
        section: 'A',
        guardianName: 'Fernando Jimenez',
        guardianContact: '+639391234567',
        address: '741 Poplar St, Tanauan City, Batangas',
        status: 'ACTIVE'
      }
    ]
  })

  // Get created students for relations
  const studentsList = await prisma.student.findMany()

  // Seed Classes
  console.log('📚 Seeding classes...')
  const classes = await prisma.class.createMany({
    data: [
      {
        subjectName: 'Mathematics',
        teacherId: teachersList[0].id,
        gradeLevel: 'Grade 11',
        section: 'A',
        scheduleTime: JSON.stringify({
          days: ['Monday', 'Wednesday', 'Friday'],
          startTime: '08:00',
          endTime: '09:30'
        }),
        roomNumber: 'Room 101'
      },
      {
        subjectName: 'English',
        teacherId: teachersList[1].id,
        gradeLevel: 'Grade 11',
        section: 'B',
        scheduleTime: JSON.stringify({
          days: ['Tuesday', 'Thursday'],
          startTime: '09:30',
          endTime: '11:00'
        }),
        roomNumber: 'Room 102'
      },
      {
        subjectName: 'Science',
        teacherId: teachersList[2].id,
        gradeLevel: 'Grade 12',
        section: 'A',
        scheduleTime: JSON.stringify({
          days: ['Monday', 'Wednesday', 'Friday'],
          startTime: '13:00',
          endTime: '14:30'
        }),
        roomNumber: 'Lab 201'
      },
      {
        subjectName: 'History',
        teacherId: teachersList[3].id,
        gradeLevel: 'Grade 12',
        section: 'B',
        scheduleTime: JSON.stringify({
          days: ['Tuesday', 'Thursday'],
          startTime: '14:30',
          endTime: '16:00'
        }),
        roomNumber: 'Room 203'
      },
      {
        subjectName: 'Computer Science',
        teacherId: teachersList[4].id,
        gradeLevel: 'Grade 11',
        section: 'A',
        scheduleTime: JSON.stringify({
          days: ['Monday', 'Friday'],
          startTime: '10:00',
          endTime: '11:30'
        }),
        roomNumber: 'Computer Lab'
      }
    ]
  })

  // Seed QR Codes for students
  console.log('🔗 Seeding QR codes...')
  const qrCodes = []
  for (const student of studentsList) {
    qrCodes.push({
      studentId: student.id,
      qrData: `TSAT_${student.studentNumber}_${Date.now()}`,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      isActive: true
    })
  }
  await prisma.qRCode.createMany({ data: qrCodes })

  // Seed Attendance Records (last 7 days)
  console.log('📅 Seeding attendance records...')
  const attendanceRecords = []
  const today = new Date()

  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)

    // Skip weekends
    if (date.getDay() === 0 || date.getDay() === 6) continue

    for (const student of studentsList.slice(0, 8)) { // First 8 students
      const timeIn = new Date(date)
      timeIn.setHours(7 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60))

      const timeOut = new Date(date)
      timeOut.setHours(15 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60))

      // Determine status based on time
      let status = 'PRESENT'
      if (timeIn.getHours() >= 8) {
        status = Math.random() > 0.7 ? 'LATE' : 'PRESENT'
      }

      // Random absences (10% chance)
      if (Math.random() < 0.1) {
        status = 'ABSENT'
      }

      attendanceRecords.push({
        studentId: student.id,
        teacherId: teachersList[0].id, // Default to first teacher
        date: date,
        timeIn: status !== 'ABSENT' ? timeIn : null,
        timeOut: status !== 'ABSENT' ? timeOut : null,
        status: status,
        remarks: status === 'LATE' ? 'Arrived late' : status === 'ABSENT' ? 'No show' : null
      })
    }
  }

  await prisma.attendance.createMany({ data: attendanceRecords })

  // Seed SMS Logs
  console.log('📱 Seeding SMS logs...')
  const smsLogs = []
  const absentAttendances = await prisma.attendance.findMany({
    where: { status: 'ABSENT' },
    include: { student: true }
  })

  for (const attendance of absentAttendances.slice(0, 5)) {
    smsLogs.push({
      recipientNumber: attendance.student.guardianContact,
      message: `Dear Parent/Guardian, your child ${attendance.student.firstName} ${attendance.student.lastName} was absent today (${attendance.date.toDateString()}). Please contact the school if this is excused. - TSAT`,
      status: Math.random() > 0.2 ? 'DELIVERED' : 'SENT',
      sentAt: new Date(attendance.date.getTime() + 2 * 60 * 60 * 1000), // 2 hours after attendance date
      deliveredAt: Math.random() > 0.2 ? new Date(attendance.date.getTime() + 2.5 * 60 * 60 * 1000) : null,
      studentId: attendance.studentId,
      attendanceId: attendance.id
    })
  }

  await prisma.sMSLog.createMany({ data: smsLogs })

  const usersList = await prisma.user.findMany()
  const classesList = await prisma.class.findMany()
  const qrCodesList = await prisma.qRCode.findMany()
  const attendanceList = await prisma.attendance.findMany()
  const smsLogsList = await prisma.sMSLog.findMany()

  console.log('✅ Database seeding completed successfully!')
  console.log(`📊 Created:`)
  console.log(`   - ${usersList.length} users`)
  console.log(`   - ${teachersList.length} teachers`)
  console.log(`   - ${studentsList.length} students`)
  console.log(`   - ${classesList.length} classes`)
  console.log(`   - ${qrCodesList.length} QR codes`)
  console.log(`   - ${attendanceList.length} attendance records`)
  console.log(`   - ${smsLogsList.length} SMS logs`)
  console.log(`   - 6 system settings`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
