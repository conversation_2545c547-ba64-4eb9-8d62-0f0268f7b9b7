import { NextRequest, NextResponse } from 'next/server'
import { cookieUtils, refreshAccessToken } from '@/lib/auth'
import { withRateLimit, rateLimiters } from '@/lib/rate-limit'

async function handler(request: NextRequest) {
  try {
    // Get refresh token from request
    const refreshToken = cookieUtils.getRefreshTokenFromRequest(request)

    if (!refreshToken) {
      // No refresh token, redirect to login
      const loginUrl = new URL('/login', request.url)
      return NextResponse.redirect(loginUrl)
    }

    // Refresh the access token
    const result = await refreshAccessToken(refreshToken)

    if (!result) {
      // Invalid refresh token, redirect to login
      const loginUrl = new URL('/login', request.url)
      return NextResponse.redirect(loginUrl)
    }

    // Check if there's a redirect URL in headers
    const redirectAfterRefresh = request.headers.get('x-redirect-after-refresh')
    const redirectUrl = redirectAfterRefresh || '/'

    // Create redirect response
    const response = NextResponse.redirect(new URL(redirectUrl, request.url))

    // Set new access token cookie
    response.cookies.set('access-token', result.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60, // 15 minutes
      path: '/'
    })

    return response

  } catch (error) {
    console.error('Refresh token API error:', error)
    // On error, redirect to login
    const loginUrl = new URL('/login', request.url)
    return NextResponse.redirect(loginUrl)
  }
}

// Apply rate limiting to the handler
export const POST = withRateLimit(handler, rateLimiters.api)

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
